package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SalesMetric represents sales data points for analytics
type SalesMetric struct {
	BaseModel
	BranchID    uuid.UUID       `json:"branch_id" gorm:"type:uuid;not null;index"`
	Date        time.Time       `json:"date" gorm:"type:date;not null;index"`
	Hour        int             `json:"hour" gorm:"not null;index"` // 0-23
	MetricType  string          `json:"metric_type" gorm:"type:varchar(50);not null;index"`
	Value       float64         `json:"value" gorm:"not null"`
	Count       int             `json:"count" gorm:"default:1"`
	Metadata    MetricsMetadata `json:"metadata" gorm:"type:jsonb"`

	// Relationships
	Branch Branch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
}

// CustomerMetric represents customer behavior analytics
type CustomerMetric struct {
	BaseModel
	BranchID       uuid.UUID       `json:"branch_id" gorm:"type:uuid;not null;index"`
	CustomerID     *uuid.UUID      `json:"customer_id" gorm:"type:uuid;index"`
	Date           time.Time       `json:"date" gorm:"type:date;not null;index"`
	VisitCount     int             `json:"visit_count" gorm:"default:1"`
	OrderCount     int             `json:"order_count" gorm:"default:0"`
	TotalSpent     float64         `json:"total_spent" gorm:"default:0"`
	AvgOrderValue  float64         `json:"avg_order_value" gorm:"default:0"`
	LastVisit      time.Time       `json:"last_visit" gorm:"not null"`
	CustomerType   string          `json:"customer_type" gorm:"type:varchar(20);default:'new'"` // new, returning, vip
	Metadata       MetricsMetadata `json:"metadata" gorm:"type:jsonb"`

	// Relationships
	Branch Branch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
}

// MenuItemMetric represents menu item performance analytics
type MenuItemMetric struct {
	BaseModel
	BranchID       uuid.UUID       `json:"branch_id" gorm:"type:uuid;not null;index"`
	MenuItemID     uuid.UUID       `json:"menu_item_id" gorm:"type:uuid;not null;index"`
	Date           time.Time       `json:"date" gorm:"type:date;not null;index"`
	OrderCount     int             `json:"order_count" gorm:"default:0"`
	QuantitySold   int             `json:"quantity_sold" gorm:"default:0"`
	Revenue        float64         `json:"revenue" gorm:"default:0"`
	AvgRating      float64         `json:"avg_rating" gorm:"default:0"`
	ViewCount      int             `json:"view_count" gorm:"default:0"`
	ConversionRate float64         `json:"conversion_rate" gorm:"default:0"` // views to orders
	Metadata       MetricsMetadata `json:"metadata" gorm:"type:jsonb"`

	// Relationships
	Branch   Branch    `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	MenuItem *MenuItem `json:"menu_item,omitempty" gorm:"foreignKey:MenuItemID"`
}

// StaffMetric represents staff performance analytics
type StaffMetric struct {
	BaseModel
	BranchID        uuid.UUID       `json:"branch_id" gorm:"type:uuid;not null;index"`
	UserID          uuid.UUID       `json:"user_id" gorm:"type:uuid;not null;index"`
	Date            time.Time       `json:"date" gorm:"type:date;not null;index"`
	OrdersProcessed int             `json:"orders_processed" gorm:"default:0"`
	Revenue         float64         `json:"revenue" gorm:"default:0"`
	HoursWorked     float64         `json:"hours_worked" gorm:"default:0"`
	AvgOrderTime    float64         `json:"avg_order_time" gorm:"default:0"` // in minutes
	CustomerRating  float64         `json:"customer_rating" gorm:"default:0"`
	Efficiency      float64         `json:"efficiency" gorm:"default:0"` // orders per hour
	Metadata        MetricsMetadata `json:"metadata" gorm:"type:jsonb"`

	// Relationships
	Branch Branch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	User   User   `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableMetric represents table utilization analytics
type TableMetric struct {
	BaseModel
	BranchID         uuid.UUID       `json:"branch_id" gorm:"type:uuid;not null;index"`
	TableID          uuid.UUID       `json:"table_id" gorm:"type:uuid;not null;index"`
	Date             time.Time       `json:"date" gorm:"type:date;not null;index"`
	Hour             int             `json:"hour" gorm:"not null;index"`
	OccupiedMinutes  int             `json:"occupied_minutes" gorm:"default:0"`
	TurnoverCount    int             `json:"turnover_count" gorm:"default:0"`
	Revenue          float64         `json:"revenue" gorm:"default:0"`
	UtilizationRate  float64         `json:"utilization_rate" gorm:"default:0"` // percentage
	AvgPartySize     float64         `json:"avg_party_size" gorm:"default:0"`
	AvgDiningTime    float64         `json:"avg_dining_time" gorm:"default:0"` // in minutes
	Metadata         MetricsMetadata `json:"metadata" gorm:"type:jsonb"`

	// Relationships
	Branch Branch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Table  Table  `json:"table,omitempty" gorm:"foreignKey:TableID"`
}

// AnalyticsReport represents a generated analytics report
type AnalyticsReport struct {
	BaseModel
	BranchID    uuid.UUID       `json:"branch_id" gorm:"type:uuid;not null;index"`
	ReportType  string          `json:"report_type" gorm:"type:varchar(50);not null"`
	Title       string          `json:"title" gorm:"type:varchar(255);not null"`
	Description string          `json:"description" gorm:"type:text"`
	StartDate   time.Time       `json:"start_date" gorm:"not null"`
	EndDate     time.Time       `json:"end_date" gorm:"not null"`
	Data        ReportData      `json:"data" gorm:"type:jsonb"`
	Status      string          `json:"status" gorm:"type:varchar(20);default:'pending'"`
	GeneratedBy uuid.UUID       `json:"generated_by" gorm:"type:uuid;not null"`
	Metadata    MetricsMetadata `json:"metadata" gorm:"type:jsonb"`

	// Relationships
	Branch    Branch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Generator User   `json:"generator,omitempty" gorm:"foreignKey:GeneratedBy"`
}

// Custom types for JSON fields
type MetricsMetadata map[string]interface{}
type ReportData map[string]interface{}

// Scan and Value methods for MetricsMetadata
func (m *MetricsMetadata) Scan(value interface{}) error {
	if value == nil {
		*m = make(MetricsMetadata)
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, m)
}

func (m MetricsMetadata) Value() (driver.Value, error) {
	if len(m) == 0 {
		return nil, nil
	}
	return json.Marshal(m)
}

// Scan and Value methods for ReportData
func (r *ReportData) Scan(value interface{}) error {
	if value == nil {
		*r = make(ReportData)
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, r)
}

func (r ReportData) Value() (driver.Value, error) {
	if len(r) == 0 {
		return nil, nil
	}
	return json.Marshal(r)
}

// TableName methods
func (SalesMetric) TableName() string     { return "sales_metrics" }
func (CustomerMetric) TableName() string { return "customer_metrics" }
func (MenuItemMetric) TableName() string { return "menu_item_metrics" }
func (StaffMetric) TableName() string    { return "staff_metrics" }
func (TableMetric) TableName() string    { return "table_metrics" }
func (AnalyticsReport) TableName() string { return "analytics_reports" }

// BeforeCreate hooks
func (sm *SalesMetric) BeforeCreate(tx *gorm.DB) error {
	if err := sm.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}
	if sm.Metadata == nil {
		sm.Metadata = make(MetricsMetadata)
	}
	return nil
}

func (cm *CustomerMetric) BeforeCreate(tx *gorm.DB) error {
	if err := cm.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}
	if cm.Metadata == nil {
		cm.Metadata = make(MetricsMetadata)
	}
	return nil
}

func (mim *MenuItemMetric) BeforeCreate(tx *gorm.DB) error {
	if err := mim.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}
	if mim.Metadata == nil {
		mim.Metadata = make(MetricsMetadata)
	}
	return nil
}

func (sm *StaffMetric) BeforeCreate(tx *gorm.DB) error {
	if err := sm.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}
	if sm.Metadata == nil {
		sm.Metadata = make(MetricsMetadata)
	}
	return nil
}

func (tm *TableMetric) BeforeCreate(tx *gorm.DB) error {
	if err := tm.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}
	if tm.Metadata == nil {
		tm.Metadata = make(MetricsMetadata)
	}
	return nil
}

func (ar *AnalyticsReport) BeforeCreate(tx *gorm.DB) error {
	if err := ar.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}
	if ar.Data == nil {
		ar.Data = make(ReportData)
	}
	if ar.Metadata == nil {
		ar.Metadata = make(MetricsMetadata)
	}
	return nil
}

// Constants for metric types
const (
	// Sales metric types
	SalesMetricRevenue      = "revenue"
	SalesMetricOrderCount   = "order_count"
	SalesMetricAvgOrderValue = "avg_order_value"
	SalesMetricCustomerCount = "customer_count"

	// Customer types
	CustomerTypeNew       = "new"
	CustomerTypeReturning = "returning"
	CustomerTypeVIP       = "vip"

	// Report types
	ReportTypeSales     = "sales"
	ReportTypeCustomer  = "customer"
	ReportTypeMenu      = "menu"
	ReportTypeStaff     = "staff"
	ReportTypeTable     = "table"
	ReportTypeInventory = "inventory"

	// Report statuses
	ReportStatusPending   = "pending"
	ReportStatusGenerating = "generating"
	ReportStatusCompleted = "completed"
	ReportStatusFailed    = "failed"
)

// Helper methods for analytics calculations
func (sm *SalesMetric) CalculateGrowthRate(previousValue float64) float64 {
	if previousValue == 0 {
		return 0
	}
	return ((sm.Value - previousValue) / previousValue) * 100
}

func (cm *CustomerMetric) UpdateAverageOrderValue() {
	if cm.OrderCount > 0 {
		cm.AvgOrderValue = cm.TotalSpent / float64(cm.OrderCount)
	}
}

func (mim *MenuItemMetric) CalculateConversionRate() {
	if mim.ViewCount > 0 {
		mim.ConversionRate = (float64(mim.OrderCount) / float64(mim.ViewCount)) * 100
	}
}

func (sm *StaffMetric) CalculateEfficiency() {
	if sm.HoursWorked > 0 {
		sm.Efficiency = float64(sm.OrdersProcessed) / sm.HoursWorked
	}
}

func (tm *TableMetric) CalculateUtilizationRate() {
	totalMinutesInHour := 60.0
	if totalMinutesInHour > 0 {
		tm.UtilizationRate = (float64(tm.OccupiedMinutes) / totalMinutesInHour) * 100
	}
}
