package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Supplier represents a supplier/vendor
type Supplier struct {
	BaseModel
	Name            string          `json:"name" gorm:"type:varchar(255);not null"`
	Contact<PERSON>erson   string          `json:"contact_person" gorm:"type:varchar(255)"`
	Email           string          `json:"email" gorm:"type:varchar(255)"`
	Phone           string          `json:"phone" gorm:"type:varchar(50)"`
	Address         string          `json:"address" gorm:"type:text"`
	PaymentTerms    string          `json:"payment_terms" gorm:"type:varchar(100)"`
	DeliveryDays    int             `json:"delivery_days" gorm:"default:3"`
	MinOrderAmount  float64         `json:"min_order_amount" gorm:"default:0"`
	Status          string          `json:"status" gorm:"type:varchar(20);default:'active'"`
	Rating          float64         `json:"rating" gorm:"default:0"`
	Notes           string          `json:"notes" gorm:"type:text"`
	Metadata        SupplierMetadata `json:"metadata" gorm:"type:jsonb"`

	// Relationships
	Ingredients []Ingredient `json:"ingredients,omitempty" gorm:"foreignKey:SupplierID"`
}

// Ingredient represents a raw ingredient or supply item
type Ingredient struct {
	BaseModel
	SupplierID      *uuid.UUID       `json:"supplier_id" gorm:"type:uuid;index"`
	Name            string           `json:"name" gorm:"type:varchar(255);not null"`
	Description     string           `json:"description" gorm:"type:text"`
	Category        string           `json:"category" gorm:"type:varchar(100);not null"`
	Unit            string           `json:"unit" gorm:"type:varchar(20);not null"` // kg, lbs, pieces, etc.
	CostPerUnit     float64          `json:"cost_per_unit" gorm:"not null"`
	SKU             string           `json:"sku" gorm:"type:varchar(100);unique"`
	Barcode         string           `json:"barcode" gorm:"type:varchar(100)"`
	MinStockLevel   float64          `json:"min_stock_level" gorm:"not null"`
	MaxStockLevel   float64          `json:"max_stock_level" gorm:"not null"`
	ReorderPoint    float64          `json:"reorder_point" gorm:"not null"`
	ShelfLife       int              `json:"shelf_life" gorm:"default:0"` // days
	StorageTemp     string           `json:"storage_temp" gorm:"type:varchar(50)"`
	IsPerishable    bool             `json:"is_perishable" gorm:"default:false"`
	IsActive        bool             `json:"is_active" gorm:"default:true"`
	AllergenInfo    AllergenData     `json:"allergen_info" gorm:"type:jsonb"`
	NutritionalInfo NutritionalData  `json:"nutritional_info" gorm:"type:jsonb"`
	Metadata        IngredientMetadata `json:"metadata" gorm:"type:jsonb"`

	// Relationships
	Supplier        *Supplier         `json:"supplier,omitempty" gorm:"foreignKey:SupplierID"`
	InventoryItems  []InventoryItem   `json:"inventory_items,omitempty" gorm:"foreignKey:IngredientID"`
	MenuIngredients []MenuIngredient  `json:"menu_ingredients,omitempty" gorm:"foreignKey:IngredientID"`
}

// InventoryItem represents current stock levels for an ingredient at a branch
type InventoryItem struct {
	BaseModel
	BranchID       uuid.UUID        `json:"branch_id" gorm:"type:uuid;not null;index"`
	IngredientID   uuid.UUID        `json:"ingredient_id" gorm:"type:uuid;not null;index"`
	CurrentStock   float64          `json:"current_stock" gorm:"not null"`
	ReservedStock  float64          `json:"reserved_stock" gorm:"default:0"`
	AvailableStock float64          `json:"available_stock" gorm:"not null"`
	LastRestocked  *time.Time       `json:"last_restocked"`
	ExpiryDate     *time.Time       `json:"expiry_date"`
	BatchNumber    string           `json:"batch_number" gorm:"type:varchar(100)"`
	Location       string           `json:"location" gorm:"type:varchar(100)"` // storage location
	Status         string           `json:"status" gorm:"type:varchar(20);default:'available'"`
	CostPerUnit    float64          `json:"cost_per_unit" gorm:"not null"`
	TotalValue     float64          `json:"total_value" gorm:"not null"`
	Metadata       InventoryMetadata `json:"metadata" gorm:"type:jsonb"`

	// Relationships
	Branch     Branch     `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Ingredient Ingredient `json:"ingredient,omitempty" gorm:"foreignKey:IngredientID"`
}

// MenuIngredient represents ingredients used in menu items
type MenuIngredient struct {
	BaseModel
	MenuItemID   uuid.UUID `json:"menu_item_id" gorm:"type:uuid;not null;index"`
	IngredientID uuid.UUID `json:"ingredient_id" gorm:"type:uuid;not null;index"`
	Quantity     float64   `json:"quantity" gorm:"not null"` // quantity needed per menu item
	Unit         string    `json:"unit" gorm:"type:varchar(20);not null"`
	IsOptional   bool      `json:"is_optional" gorm:"default:false"`
	Cost         float64   `json:"cost" gorm:"not null"` // cost for this quantity

	// Relationships
	MenuItem   MenuItem   `json:"menu_item,omitempty" gorm:"foreignKey:MenuItemID"`
	Ingredient Ingredient `json:"ingredient,omitempty" gorm:"foreignKey:IngredientID"`
}

// PurchaseOrder represents an order to suppliers
type PurchaseOrder struct {
	BaseModel
	BranchID       uuid.UUID           `json:"branch_id" gorm:"type:uuid;not null;index"`
	SupplierID     uuid.UUID           `json:"supplier_id" gorm:"type:uuid;not null;index"`
	OrderNumber    string              `json:"order_number" gorm:"type:varchar(100);unique;not null"`
	Status         string              `json:"status" gorm:"type:varchar(20);default:'pending'"`
	OrderDate      time.Time           `json:"order_date" gorm:"not null"`
	ExpectedDate   *time.Time          `json:"expected_date"`
	ReceivedDate   *time.Time          `json:"received_date"`
	Subtotal       float64             `json:"subtotal" gorm:"not null"`
	TaxAmount      float64             `json:"tax_amount" gorm:"default:0"`
	ShippingCost   float64             `json:"shipping_cost" gorm:"default:0"`
	Total          float64             `json:"total" gorm:"not null"`
	Notes          string              `json:"notes" gorm:"type:text"`
	CreatedBy      uuid.UUID           `json:"created_by" gorm:"type:uuid;not null"`
	ApprovedBy     *uuid.UUID          `json:"approved_by" gorm:"type:uuid"`
	ReceivedBy     *uuid.UUID          `json:"received_by" gorm:"type:uuid"`
	Metadata       PurchaseOrderMetadata `json:"metadata" gorm:"type:jsonb"`

	// Relationships
	Branch    Branch              `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Supplier  Supplier            `json:"supplier,omitempty" gorm:"foreignKey:SupplierID"`
	Creator   User                `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
	Approver  *User               `json:"approver,omitempty" gorm:"foreignKey:ApprovedBy"`
	Receiver  *User               `json:"receiver,omitempty" gorm:"foreignKey:ReceivedBy"`
	Items     []PurchaseOrderItem `json:"items,omitempty" gorm:"foreignKey:PurchaseOrderID"`
}

// PurchaseOrderItem represents items in a purchase order
type PurchaseOrderItem struct {
	BaseModel
	PurchaseOrderID uuid.UUID `json:"purchase_order_id" gorm:"type:uuid;not null;index"`
	IngredientID    uuid.UUID `json:"ingredient_id" gorm:"type:uuid;not null;index"`
	Quantity        float64   `json:"quantity" gorm:"not null"`
	Unit            string    `json:"unit" gorm:"type:varchar(20);not null"`
	UnitPrice       float64   `json:"unit_price" gorm:"not null"`
	Total           float64   `json:"total" gorm:"not null"`
	ReceivedQty     float64   `json:"received_qty" gorm:"default:0"`
	Status          string    `json:"status" gorm:"type:varchar(20);default:'pending'"`

	// Relationships
	PurchaseOrder PurchaseOrder `json:"purchase_order,omitempty" gorm:"foreignKey:PurchaseOrderID"`
	Ingredient    Ingredient    `json:"ingredient,omitempty" gorm:"foreignKey:IngredientID"`
}

// StockMovement represents inventory movements (in/out)
type StockMovement struct {
	BaseModel
	BranchID        uuid.UUID         `json:"branch_id" gorm:"type:uuid;not null;index"`
	IngredientID    uuid.UUID         `json:"ingredient_id" gorm:"type:uuid;not null;index"`
	MovementType    string            `json:"movement_type" gorm:"type:varchar(20);not null"` // in, out, adjustment, waste
	Quantity        float64           `json:"quantity" gorm:"not null"`
	Unit            string            `json:"unit" gorm:"type:varchar(20);not null"`
	Reason          string            `json:"reason" gorm:"type:varchar(100);not null"`
	Reference       string            `json:"reference" gorm:"type:varchar(100)"` // order_id, po_id, etc.
	ReferenceType   string            `json:"reference_type" gorm:"type:varchar(50)"`
	CostPerUnit     float64           `json:"cost_per_unit" gorm:"not null"`
	TotalCost       float64           `json:"total_cost" gorm:"not null"`
	PreviousStock   float64           `json:"previous_stock" gorm:"not null"`
	NewStock        float64           `json:"new_stock" gorm:"not null"`
	PerformedBy     uuid.UUID         `json:"performed_by" gorm:"type:uuid;not null"`
	Notes           string            `json:"notes" gorm:"type:text"`
	Metadata        MovementMetadata  `json:"metadata" gorm:"type:jsonb"`

	// Relationships
	Branch     Branch     `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Ingredient Ingredient `json:"ingredient,omitempty" gorm:"foreignKey:IngredientID"`
	User       User       `json:"user,omitempty" gorm:"foreignKey:PerformedBy"`
}

// WasteRecord represents food waste tracking
type WasteRecord struct {
	BaseModel
	BranchID     uuid.UUID        `json:"branch_id" gorm:"type:uuid;not null;index"`
	IngredientID uuid.UUID        `json:"ingredient_id" gorm:"type:uuid;not null;index"`
	Quantity     float64          `json:"quantity" gorm:"not null"`
	Unit         string           `json:"unit" gorm:"type:varchar(20);not null"`
	Reason       string           `json:"reason" gorm:"type:varchar(100);not null"` // expired, spoiled, overproduction
	Cost         float64          `json:"cost" gorm:"not null"`
	RecordedBy   uuid.UUID        `json:"recorded_by" gorm:"type:uuid;not null"`
	WasteDate    time.Time        `json:"waste_date" gorm:"not null"`
	Notes        string           `json:"notes" gorm:"type:text"`
	Metadata     WasteMetadata    `json:"metadata" gorm:"type:jsonb"`

	// Relationships
	Branch     Branch     `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Ingredient Ingredient `json:"ingredient,omitempty" gorm:"foreignKey:IngredientID"`
	User       User       `json:"user,omitempty" gorm:"foreignKey:RecordedBy"`
}

// Custom JSON types
type SupplierMetadata map[string]interface{}
type IngredientMetadata map[string]interface{}
type InventoryMetadata map[string]interface{}
type PurchaseOrderMetadata map[string]interface{}
type MovementMetadata map[string]interface{}
type WasteMetadata map[string]interface{}
type AllergenData []string
type NutritionalData map[string]interface{}

// Scan and Value methods for custom types
func (s *SupplierMetadata) Scan(value interface{}) error {
	return scanJSONField(value, s)
}
func (s SupplierMetadata) Value() (driver.Value, error) {
	return valueJSONField(s)
}

func (i *IngredientMetadata) Scan(value interface{}) error {
	return scanJSONField(value, i)
}
func (i IngredientMetadata) Value() (driver.Value, error) {
	return valueJSONField(i)
}

func (i *InventoryMetadata) Scan(value interface{}) error {
	return scanJSONField(value, i)
}
func (i InventoryMetadata) Value() (driver.Value, error) {
	return valueJSONField(i)
}

func (p *PurchaseOrderMetadata) Scan(value interface{}) error {
	return scanJSONField(value, p)
}
func (p PurchaseOrderMetadata) Value() (driver.Value, error) {
	return valueJSONField(p)
}

func (m *MovementMetadata) Scan(value interface{}) error {
	return scanJSONField(value, m)
}
func (m MovementMetadata) Value() (driver.Value, error) {
	return valueJSONField(m)
}

func (w *WasteMetadata) Scan(value interface{}) error {
	return scanJSONField(value, w)
}
func (w WasteMetadata) Value() (driver.Value, error) {
	return valueJSONField(w)
}

func (a *AllergenData) Scan(value interface{}) error {
	return scanJSONField(value, a)
}
func (a AllergenData) Value() (driver.Value, error) {
	return valueJSONField(a)
}

func (n *NutritionalData) Scan(value interface{}) error {
	return scanJSONField(value, n)
}
func (n NutritionalData) Value() (driver.Value, error) {
	return valueJSONField(n)
}

// Helper functions for JSON scanning
func scanJSONField(value interface{}, dest interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, dest)
}

func valueJSONField(src interface{}) (driver.Value, error) {
	return json.Marshal(src)
}

// TableName methods
func (Supplier) TableName() string          { return "suppliers" }
func (Ingredient) TableName() string        { return "ingredients" }
func (InventoryItem) TableName() string     { return "inventory_items" }
func (MenuIngredient) TableName() string    { return "menu_ingredients" }
func (PurchaseOrder) TableName() string     { return "purchase_orders" }
func (PurchaseOrderItem) TableName() string { return "purchase_order_items" }
func (StockMovement) TableName() string     { return "stock_movements" }
func (WasteRecord) TableName() string       { return "waste_records" }

// Constants
const (
	// Supplier statuses
	SupplierStatusActive   = "active"
	SupplierStatusInactive = "inactive"
	SupplierStatusBlocked  = "blocked"

	// Inventory statuses
	InventoryStatusAvailable = "available"
	InventoryStatusReserved  = "reserved"
	InventoryStatusExpired   = "expired"
	InventoryStatusDamaged   = "damaged"

	// Purchase order statuses
	POStatusPending   = "pending"
	POStatusApproved  = "approved"
	POStatusOrdered   = "ordered"
	POStatusReceived  = "received"
	POStatusCancelled = "cancelled"

	// Movement types
	MovementTypeIn         = "in"
	MovementTypeOut        = "out"
	MovementTypeAdjustment = "adjustment"
	MovementTypeWaste      = "waste"

	// Waste reasons
	WasteReasonExpired       = "expired"
	WasteReasonSpoiled       = "spoiled"
	WasteReasonOverproduction = "overproduction"
	WasteReasonDamaged       = "damaged"
	WasteReasonOther         = "other"
)

// BeforeCreate hooks
func (s *Supplier) BeforeCreate(tx *gorm.DB) error {
	if err := s.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}
	if s.Metadata == nil {
		s.Metadata = make(SupplierMetadata)
	}
	return nil
}

func (i *Ingredient) BeforeCreate(tx *gorm.DB) error {
	if err := i.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}
	if i.Metadata == nil {
		i.Metadata = make(IngredientMetadata)
	}
	if i.AllergenInfo == nil {
		i.AllergenInfo = make(AllergenData, 0)
	}
	if i.NutritionalInfo == nil {
		i.NutritionalInfo = make(NutritionalData)
	}
	return nil
}

func (ii *InventoryItem) BeforeCreate(tx *gorm.DB) error {
	if err := ii.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}
	ii.CalculateAvailableStock()
	ii.CalculateTotalValue()
	if ii.Metadata == nil {
		ii.Metadata = make(InventoryMetadata)
	}
	return nil
}

func (po *PurchaseOrder) BeforeCreate(tx *gorm.DB) error {
	if err := po.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}
	if po.OrderNumber == "" {
		po.OrderNumber = po.generateOrderNumber()
	}
	if po.Metadata == nil {
		po.Metadata = make(PurchaseOrderMetadata)
	}
	return nil
}

// Helper methods
func (ii *InventoryItem) CalculateAvailableStock() {
	ii.AvailableStock = ii.CurrentStock - ii.ReservedStock
}

func (ii *InventoryItem) CalculateTotalValue() {
	ii.TotalValue = ii.CurrentStock * ii.CostPerUnit
}

func (ii *InventoryItem) IsLowStock(ingredient *Ingredient) bool {
	if ingredient == nil {
		return false
	}
	return ii.AvailableStock <= ingredient.ReorderPoint
}

func (ii *InventoryItem) IsExpiringSoon(days int) bool {
	if ii.ExpiryDate == nil {
		return false
	}
	return time.Until(*ii.ExpiryDate) <= time.Duration(days)*24*time.Hour
}

func (po *PurchaseOrder) generateOrderNumber() string {
	return "PO-" + time.Now().Format("20060102") + "-" + po.ID.String()[:8]
}

func (po *PurchaseOrder) CalculateTotal() {
	po.Total = po.Subtotal + po.TaxAmount + po.ShippingCost
}

func (poi *PurchaseOrderItem) CalculateTotal() {
	poi.Total = poi.Quantity * poi.UnitPrice
}

func (sm *StockMovement) CalculateTotalCost() {
	sm.TotalCost = sm.Quantity * sm.CostPerUnit
}
