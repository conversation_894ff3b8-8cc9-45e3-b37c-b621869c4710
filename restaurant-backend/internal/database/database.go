package database

import (
	"time"

	"restaurant-backend/internal/models"

	"github.com/sirupsen/logrus"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Initialize initializes the database connection
func Initialize(dsn string, log *logrus.Logger) (*gorm.DB, error) {
	// Configure GORM logger
	gormLogger := logger.New(
		log,
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  false,
		},
	)

	// Open database connection
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})
	if err != nil {
		return nil, err
	}

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	// Set connection pool settings
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// Test the connection
	if err := sqlDB.Ping(); err != nil {
		return nil, err
	}

	log.Info("Database connection established successfully")
	return db, nil
}

// Migrate runs database migrations
func Migrate(db *gorm.DB) error {
	// Enable UUID extension
	if err := db.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error; err != nil {
		return err
	}

	// Auto-migrate all models
	err := db.AutoMigrate(
		&models.Merchant{},
		&models.Branch{},
		&models.User{},
		&models.Role{},
		&models.MenuCategory{},
		&models.MenuItem{},
		&models.Order{},
		&models.OrderItem{},
		&models.Payment{},
		&models.Reservation{},
		&models.Table{},
		&models.TableArea{},
		&models.Review{},
	)
	if err != nil {
		return err
	}

	// Create indexes for better performance
	if err := createIndexes(db); err != nil {
		return err
	}

	// Seed default data
	if err := seedDefaultData(db); err != nil {
		return err
	}

	return nil
}

// createIndexes creates additional database indexes for performance
func createIndexes(db *gorm.DB) error {
	indexes := []string{
		// Orders indexes
		"CREATE INDEX IF NOT EXISTS idx_orders_branch_status ON orders(branch_id, status)",
		"CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_orders_customer_phone ON orders(customer_phone)",
		
		// Menu items indexes
		"CREATE INDEX IF NOT EXISTS idx_menu_items_branch_category ON menu_items(branch_id, category_id)",
		"CREATE INDEX IF NOT EXISTS idx_menu_items_available ON menu_items(branch_id) WHERE is_available = true",
		"CREATE INDEX IF NOT EXISTS idx_menu_items_slug ON menu_items(branch_id, slug)",
		
		// Reservations indexes
		"CREATE INDEX IF NOT EXISTS idx_reservations_branch_date ON reservations(branch_id, reservation_date)",
		"CREATE INDEX IF NOT EXISTS idx_reservations_status ON reservations(branch_id, status)",
		
		// Reviews indexes
		"CREATE INDEX IF NOT EXISTS idx_reviews_branch_rating ON reviews(branch_id, rating)",
		"CREATE INDEX IF NOT EXISTS idx_reviews_created_at ON reviews(created_at)",
		
		// Tables indexes
		"CREATE INDEX IF NOT EXISTS idx_tables_branch_status ON tables(branch_id, status)",
		"CREATE INDEX IF NOT EXISTS idx_tables_active ON tables(branch_id) WHERE is_active = true",
		
		// Users indexes
		"CREATE INDEX IF NOT EXISTS idx_users_branch_status ON users(branch_id, status)",
		"CREATE INDEX IF NOT EXISTS idx_users_role ON users(role_id)",
	}

	for _, index := range indexes {
		if err := db.Exec(index).Error; err != nil {
			return err
		}
	}

	return nil
}

// seedDefaultData seeds the database with default data
func seedDefaultData(db *gorm.DB) error {
	// Check if we already have data
	var count int64
	db.Model(&models.Merchant{}).Count(&count)
	if count > 0 {
		return nil // Data already exists
	}

	// Create default permissions
	if err := seedDefaultRoles(db); err != nil {
		return err
	}

	// Create demo merchant and branch
	if err := seedDemoData(db); err != nil {
		return err
	}

	return nil
}

// seedDefaultRoles creates default roles and permissions
func seedDefaultRoles(db *gorm.DB) error {
	// This will be implemented when we create the demo data
	return nil
}

// seedDemoData creates demo merchant, branch, and sample data
func seedDemoData(db *gorm.DB) error {
	// Create demo merchant
	merchant := &models.Merchant{
		Name:             "Demo Restaurant Group",
		Slug:             "demo-restaurant-group",
		Email:            "<EMAIL>",
		Phone:            "******-0123",
		SubscriptionPlan: models.SubscriptionPlanPremium,
		IsActive:         true,
		Address: models.Address{
			Street:  "123 Main Street",
			City:    "New York",
			State:   "NY",
			ZipCode: "10001",
			Country: "USA",
		},
	}

	if err := db.Create(merchant).Error; err != nil {
		return err
	}

	// Create demo branch
	branch := &models.Branch{
		MerchantID: merchant.ID,
		Name:       "Downtown Location",
		Slug:       "downtown-location",
		Email:      "<EMAIL>",
		Phone:      "******-0124",
		Timezone:   "America/New_York",
		IsActive:   true,
		Address: models.Address{
			Street:  "456 Broadway",
			City:    "New York",
			State:   "NY",
			ZipCode: "10013",
			Country: "USA",
		},
	}

	if err := db.Create(branch).Error; err != nil {
		return err
	}

	// Create default admin role
	adminRole := &models.Role{
		MerchantID:  merchant.ID,
		Name:        "Administrator",
		Description: "Full access to all features",
		Permissions: models.PermissionsData{
			models.PermissionViewDashboard,
			models.PermissionManageOrders,
			models.PermissionManageMenu,
			models.PermissionManageStaff,
			models.PermissionManageReservations,
			models.PermissionManageTables,
			models.PermissionManageReviews,
			models.PermissionViewReports,
			models.PermissionManageSettings,
		},
		IsActive: true,
	}

	if err := db.Create(adminRole).Error; err != nil {
		return err
	}

	// Create demo admin user
	adminUser := &models.User{
		MerchantID:  merchant.ID,
		BranchID:    &branch.ID,
		Email:       "<EMAIL>",
		FirstName:   "Admin",
		LastName:    "User",
		Phone:       "******-0125",
		RoleID:      adminRole.ID,
		Position:    "General Manager",
		Department:  "Management",
		EmployeeID:  "EMP001",
		Status:      models.UserStatusActive,
		HireDate:    time.Now().AddDate(-1, 0, 0), // Hired 1 year ago
	}

	// Set default password
	if err := adminUser.SetPassword("admin123"); err != nil {
		return err
	}

	if err := db.Create(adminUser).Error; err != nil {
		return err
	}

	// Create sample menu categories
	categories := []*models.MenuCategory{
		{
			BranchID:    branch.ID,
			Name:        "Appetizers",
			Slug:        "appetizers",
			Description: "Start your meal with our delicious appetizers",
			SortOrder:   1,
			IsActive:    true,
		},
		{
			BranchID:    branch.ID,
			Name:        "Main Courses",
			Slug:        "main-courses",
			Description: "Our signature main dishes",
			SortOrder:   2,
			IsActive:    true,
		},
		{
			BranchID:    branch.ID,
			Name:        "Desserts",
			Slug:        "desserts",
			Description: "Sweet endings to your meal",
			SortOrder:   3,
			IsActive:    true,
		},
		{
			BranchID:    branch.ID,
			Name:        "Beverages",
			Slug:        "beverages",
			Description: "Refreshing drinks and beverages",
			SortOrder:   4,
			IsActive:    true,
		},
	}

	for _, category := range categories {
		if err := db.Create(category).Error; err != nil {
			return err
		}
	}

	// Create sample menu items
	menuItems := []*models.MenuItem{
		{
			BranchID:        branch.ID,
			CategoryID:      &categories[0].ID, // Appetizers
			Name:            "Caesar Salad",
			Slug:            "caesar-salad",
			Description:     "Fresh romaine lettuce with parmesan cheese and croutons",
			Price:           12.99,
			PreparationTime: intPtr(10),
			IsAvailable:     true,
			IsVegetarian:    true,
			Tags:            models.TagsData{"popular", "healthy"},
		},
		{
			BranchID:        branch.ID,
			CategoryID:      &categories[1].ID, // Main Courses
			Name:            "Grilled Salmon",
			Slug:            "grilled-salmon",
			Description:     "Fresh Atlantic salmon grilled to perfection",
			Price:           24.99,
			PreparationTime: intPtr(20),
			IsAvailable:     true,
			IsGlutenFree:    true,
			Tags:            models.TagsData{"signature", "healthy"},
		},
		{
			BranchID:        branch.ID,
			CategoryID:      &categories[2].ID, // Desserts
			Name:            "Chocolate Cake",
			Slug:            "chocolate-cake",
			Description:     "Rich chocolate cake with vanilla ice cream",
			Price:           8.99,
			PreparationTime: intPtr(5),
			IsAvailable:     true,
			IsVegetarian:    true,
			Tags:            models.TagsData{"popular", "sweet"},
		},
	}

	for _, item := range menuItems {
		if err := db.Create(item).Error; err != nil {
			return err
		}
	}

	// Create sample table areas
	areas := []*models.TableArea{
		{
			BranchID:    branch.ID,
			Name:        "Dining Area",
			Description: "Main dining room",
			Color:       "#8a745c",
			IsActive:    true,
		},
		{
			BranchID:    branch.ID,
			Name:        "Outdoor Patio",
			Description: "Outdoor seating area",
			Color:       "#6b8e23",
			IsActive:    true,
		},
	}

	for _, area := range areas {
		if err := db.Create(area).Error; err != nil {
			return err
		}
	}

	return nil
}

// Helper function to create int pointer
func intPtr(i int) *int {
	return &i
}
