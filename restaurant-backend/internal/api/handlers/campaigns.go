package handlers

import (
	"net/http"

	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// CampaignHandler handles campaign-related HTTP requests
type CampaignHandler struct {
	campaignService *services.CampaignService
	logger          *logrus.Logger
}

// NewCampaignHandler creates a new campaign handler
func NewCampaignHandler(campaignService *services.CampaignService, logger *logrus.Logger) *CampaignHandler {
	return &CampaignHandler{
		campaignService: campaignService,
		logger:          logger,
	}
}

// GetCampaigns godoc
// @Summary Get campaigns
// @Description Get campaigns for a merchant
// @Tags campaigns
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param type query string false "Type filter"
// @Param status query string false "Status filter"
// @Param target_audience query string false "Target audience filter"
// @Param template_id query string false "Template ID filter"
// @Param segment_id query string false "Segment ID filter"
// @Param search query string false "Search filter"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.CampaignsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/campaigns [get]
func (h *CampaignHandler) GetCampaigns(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	var filters types.CampaignFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	campaigns, err := h.campaignService.GetCampaigns(c.Request.Context(), merchantID, filters)
	if err != nil {
		h.logger.Error("Failed to get campaigns: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get campaigns"})
		return
	}

	c.JSON(http.StatusOK, campaigns)
}

// GetCampaign godoc
// @Summary Get campaign by ID
// @Description Get a specific campaign by ID
// @Tags campaigns
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param campaignId path string true "Campaign ID"
// @Success 200 {object} types.CampaignResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/campaigns/{campaignId} [get]
func (h *CampaignHandler) GetCampaign(c *gin.Context) {
	campaignID, err := uuid.Parse(c.Param("campaignId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid campaign ID"})
		return
	}

	campaign, err := h.campaignService.GetCampaignByID(c.Request.Context(), campaignID)
	if err != nil {
		h.logger.Error("Failed to get campaign: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Campaign not found"})
		return
	}

	c.JSON(http.StatusOK, campaign)
}

// CreateCampaign godoc
// @Summary Create a new campaign
// @Description Create a new campaign for a merchant
// @Tags campaigns
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param campaign body types.CreateCampaignRequest true "Campaign data"
// @Success 201 {object} types.CampaignResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/campaigns [post]
func (h *CampaignHandler) CreateCampaign(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	var req types.CreateCampaignRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	campaign, err := h.campaignService.CreateCampaign(c.Request.Context(), merchantID, req)
	if err != nil {
		h.logger.Error("Failed to create campaign: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create campaign"})
		return
	}

	c.JSON(http.StatusCreated, campaign)
}

// UpdateCampaign godoc
// @Summary Update a campaign
// @Description Update an existing campaign
// @Tags campaigns
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param campaignId path string true "Campaign ID"
// @Param campaign body types.UpdateCampaignRequest true "Campaign update data"
// @Success 200 {object} types.CampaignResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/campaigns/{campaignId} [put]
func (h *CampaignHandler) UpdateCampaign(c *gin.Context) {
	campaignID, err := uuid.Parse(c.Param("campaignId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid campaign ID"})
		return
	}

	var req types.UpdateCampaignRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	campaign, err := h.campaignService.UpdateCampaign(c.Request.Context(), campaignID, req)
	if err != nil {
		h.logger.Error("Failed to update campaign: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update campaign"})
		return
	}

	c.JSON(http.StatusOK, campaign)
}

// DeleteCampaign godoc
// @Summary Delete a campaign
// @Description Delete an existing campaign
// @Tags campaigns
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param campaignId path string true "Campaign ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/campaigns/{campaignId} [delete]
func (h *CampaignHandler) DeleteCampaign(c *gin.Context) {
	campaignID, err := uuid.Parse(c.Param("campaignId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid campaign ID"})
		return
	}

	if err := h.campaignService.DeleteCampaign(c.Request.Context(), campaignID); err != nil {
		h.logger.Error("Failed to delete campaign: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete campaign"})
		return
	}

	c.Status(http.StatusNoContent)
}

// ExecuteCampaign godoc
// @Summary Execute a campaign
// @Description Execute an existing campaign
// @Tags campaigns
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param campaignId path string true "Campaign ID"
// @Param request body types.ExecuteCampaignRequest true "Execution data"
// @Success 200 {object} types.CampaignResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/campaigns/{campaignId}/execute [post]
func (h *CampaignHandler) ExecuteCampaign(c *gin.Context) {
	campaignID, err := uuid.Parse(c.Param("campaignId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid campaign ID"})
		return
	}

	var req types.ExecuteCampaignRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	campaign, err := h.campaignService.ExecuteCampaign(c.Request.Context(), campaignID, req)
	if err != nil {
		h.logger.Error("Failed to execute campaign: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to execute campaign"})
		return
	}

	c.JSON(http.StatusOK, campaign)
}

// GetTemplates godoc
// @Summary Get communication templates
// @Description Get communication templates for a merchant
// @Tags templates
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param type query string false "Type filter"
// @Param category query string false "Category filter"
// @Param is_default query bool false "Default filter"
// @Param is_active query bool false "Active filter"
// @Param search query string false "Search filter"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.TemplatesResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/communication-templates [get]
func (h *CampaignHandler) GetTemplates(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	var filters types.TemplateFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	templates, err := h.campaignService.GetTemplates(c.Request.Context(), merchantID, filters)
	if err != nil {
		h.logger.Error("Failed to get templates: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get templates"})
		return
	}

	c.JSON(http.StatusOK, templates)
}

// GetTemplate godoc
// @Summary Get template by ID
// @Description Get a specific template by ID
// @Tags templates
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param templateId path string true "Template ID"
// @Success 200 {object} types.TemplateResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/communication-templates/{templateId} [get]
func (h *CampaignHandler) GetTemplate(c *gin.Context) {
	templateID, err := uuid.Parse(c.Param("templateId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	template, err := h.campaignService.GetTemplateByID(c.Request.Context(), templateID)
	if err != nil {
		h.logger.Error("Failed to get template: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	}

	c.JSON(http.StatusOK, template)
}

// CreateTemplate godoc
// @Summary Create a new template
// @Description Create a new communication template for a merchant
// @Tags templates
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param template body types.CreateTemplateRequest true "Template data"
// @Success 201 {object} types.TemplateResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/communication-templates [post]
func (h *CampaignHandler) CreateTemplate(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	var req types.CreateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	template, err := h.campaignService.CreateTemplate(c.Request.Context(), merchantID, req)
	if err != nil {
		h.logger.Error("Failed to create template: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create template"})
		return
	}

	c.JSON(http.StatusCreated, template)
}

// UpdateTemplate godoc
// @Summary Update a template
// @Description Update an existing communication template
// @Tags templates
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param templateId path string true "Template ID"
// @Param template body types.UpdateTemplateRequest true "Template update data"
// @Success 200 {object} types.TemplateResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/communication-templates/{templateId} [put]
func (h *CampaignHandler) UpdateTemplate(c *gin.Context) {
	templateID, err := uuid.Parse(c.Param("templateId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	var req types.UpdateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	template, err := h.campaignService.UpdateTemplate(c.Request.Context(), templateID, req)
	if err != nil {
		h.logger.Error("Failed to update template: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update template"})
		return
	}

	c.JSON(http.StatusOK, template)
}

// DeleteTemplate godoc
// @Summary Delete a template
// @Description Delete an existing communication template
// @Tags templates
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param templateId path string true "Template ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/communication-templates/{templateId} [delete]
func (h *CampaignHandler) DeleteTemplate(c *gin.Context) {
	templateID, err := uuid.Parse(c.Param("templateId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	if err := h.campaignService.DeleteTemplate(c.Request.Context(), templateID); err != nil {
		h.logger.Error("Failed to delete template: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete template"})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetSegments godoc
// @Summary Get campaign segments
// @Description Get campaign segments for a merchant
// @Tags segments
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param type query string false "Type filter"
// @Param is_active query bool false "Active filter"
// @Param search query string false "Search filter"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.SegmentsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/campaign-segments [get]
func (h *CampaignHandler) GetSegments(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	var filters types.SegmentFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	segments, err := h.campaignService.GetSegments(c.Request.Context(), merchantID, filters)
	if err != nil {
		h.logger.Error("Failed to get segments: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get segments"})
		return
	}

	c.JSON(http.StatusOK, segments)
}

// GetSegment godoc
// @Summary Get segment by ID
// @Description Get a specific segment by ID
// @Tags segments
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param segmentId path string true "Segment ID"
// @Success 200 {object} types.SegmentResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/campaign-segments/{segmentId} [get]
func (h *CampaignHandler) GetSegment(c *gin.Context) {
	segmentID, err := uuid.Parse(c.Param("segmentId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid segment ID"})
		return
	}

	segment, err := h.campaignService.GetSegmentByID(c.Request.Context(), segmentID)
	if err != nil {
		h.logger.Error("Failed to get segment: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Segment not found"})
		return
	}

	c.JSON(http.StatusOK, segment)
}

// CreateSegment godoc
// @Summary Create a new segment
// @Description Create a new campaign segment for a merchant
// @Tags segments
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param segment body types.CreateSegmentRequest true "Segment data"
// @Success 201 {object} types.SegmentResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/campaign-segments [post]
func (h *CampaignHandler) CreateSegment(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	var req types.CreateSegmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	segment, err := h.campaignService.CreateSegment(c.Request.Context(), merchantID, req)
	if err != nil {
		h.logger.Error("Failed to create segment: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create segment"})
		return
	}

	c.JSON(http.StatusCreated, segment)
}

// UpdateSegment godoc
// @Summary Update a segment
// @Description Update an existing campaign segment
// @Tags segments
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param segmentId path string true "Segment ID"
// @Param segment body types.UpdateSegmentRequest true "Segment update data"
// @Success 200 {object} types.SegmentResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/campaign-segments/{segmentId} [put]
func (h *CampaignHandler) UpdateSegment(c *gin.Context) {
	segmentID, err := uuid.Parse(c.Param("segmentId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid segment ID"})
		return
	}

	var req types.UpdateSegmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	segment, err := h.campaignService.UpdateSegment(c.Request.Context(), segmentID, req)
	if err != nil {
		h.logger.Error("Failed to update segment: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update segment"})
		return
	}

	c.JSON(http.StatusOK, segment)
}

// DeleteSegment godoc
// @Summary Delete a segment
// @Description Delete an existing campaign segment
// @Tags segments
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param segmentId path string true "Segment ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/campaign-segments/{segmentId} [delete]
func (h *CampaignHandler) DeleteSegment(c *gin.Context) {
	segmentID, err := uuid.Parse(c.Param("segmentId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid segment ID"})
		return
	}

	if err := h.campaignService.DeleteSegment(c.Request.Context(), segmentID); err != nil {
		h.logger.Error("Failed to delete segment: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete segment"})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetSegmentCustomers godoc
// @Summary Get segment customers
// @Description Get customers in a specific segment
// @Tags segments
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param segmentId path string true "Segment ID"
// @Success 200 {array} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/campaign-segments/{segmentId}/customers [get]
func (h *CampaignHandler) GetSegmentCustomers(c *gin.Context) {
	segmentID, err := uuid.Parse(c.Param("segmentId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid segment ID"})
		return
	}

	customers, err := h.campaignService.GetSegmentCustomers(c.Request.Context(), segmentID)
	if err != nil {
		h.logger.Error("Failed to get segment customers: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get segment customers"})
		return
	}

	c.JSON(http.StatusOK, customers)
}

// GetSegmentEmails godoc
// @Summary Get segment emails
// @Description Get email addresses of customers in a specific segment
// @Tags segments
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param segmentId path string true "Segment ID"
// @Success 200 {array} string
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/campaign-segments/{segmentId}/emails [get]
func (h *CampaignHandler) GetSegmentEmails(c *gin.Context) {
	segmentID, err := uuid.Parse(c.Param("segmentId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid segment ID"})
		return
	}

	emails, err := h.campaignService.GetSegmentEmails(c.Request.Context(), segmentID)
	if err != nil {
		h.logger.Error("Failed to get segment emails: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get segment emails"})
		return
	}

	c.JSON(http.StatusOK, emails)
}

// GetSegmentPhones godoc
// @Summary Get segment phones
// @Description Get phone numbers of customers in a specific segment
// @Tags segments
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param segmentId path string true "Segment ID"
// @Success 200 {array} string
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/campaign-segments/{segmentId}/phones [get]
func (h *CampaignHandler) GetSegmentPhones(c *gin.Context) {
	segmentID, err := uuid.Parse(c.Param("segmentId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid segment ID"})
		return
	}

	phones, err := h.campaignService.GetSegmentPhones(c.Request.Context(), segmentID)
	if err != nil {
		h.logger.Error("Failed to get segment phones: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get segment phones"})
		return
	}

	c.JSON(http.StatusOK, phones)
}

// GetCommunicationAnalyticsOverview godoc
// @Summary Get communication analytics overview
// @Description Get overview of communication analytics for a merchant
// @Tags analytics
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Success 200 {object} types.CommunicationAnalyticsOverview
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/communication-analytics/overview [get]
func (h *CampaignHandler) GetCommunicationAnalyticsOverview(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	overview, err := h.campaignService.GetCommunicationAnalyticsOverview(c.Request.Context(), merchantID)
	if err != nil {
		h.logger.Error("Failed to get communication analytics overview: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get analytics overview"})
		return
	}

	c.JSON(http.StatusOK, overview)
}

// GetCampaignAnalytics godoc
// @Summary Get campaign analytics
// @Description Get detailed analytics for a specific campaign
// @Tags analytics
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param campaignId path string true "Campaign ID"
// @Success 200 {object} types.CampaignAnalyticsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/communication-analytics/campaigns/{campaignId} [get]
func (h *CampaignHandler) GetCampaignAnalytics(c *gin.Context) {
	campaignID, err := uuid.Parse(c.Param("campaignId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid campaign ID"})
		return
	}

	analytics, err := h.campaignService.GetCampaignAnalytics(c.Request.Context(), campaignID)
	if err != nil {
		h.logger.Error("Failed to get campaign analytics: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get campaign analytics"})
		return
	}

	c.JSON(http.StatusOK, analytics)
}