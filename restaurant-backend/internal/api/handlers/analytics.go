package handlers

import (
	"net/http"
	"strconv"
	"time"

	"restaurant-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// AnalyticsHandler handles analytics-related HTTP requests
type AnalyticsHandler struct {
	analyticsService *services.AnalyticsService
	logger           *logrus.Logger
}

// NewAnalyticsHandler creates a new analytics handler
func NewAnalyticsHandler(analyticsService *services.AnalyticsService, logger *logrus.Logger) *AnalyticsHandler {
	return &AnalyticsHandler{
		analyticsService: analyticsService,
		logger:           logger,
	}
}

// GetDashboard godoc
// @Summary Get analytics dashboard
// @Description Get comprehensive analytics dashboard data for a branch
// @Tags analytics
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Success 200 {object} services.DashboardData
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/analytics/dashboard [get]
func (h *AnalyticsHandler) GetDashboard(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	dashboard, err := h.analyticsService.GetDashboardData(c.Request.Context(), branchID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get dashboard data")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get dashboard data"})
		return
	}

	c.JSON(http.StatusOK, dashboard)
}

// GetSalesReport godoc
// @Summary Get sales report
// @Description Generate a comprehensive sales report for a specified date range
// @Tags analytics
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param start_date query string true "Start date (YYYY-MM-DD)"
// @Param end_date query string true "End date (YYYY-MM-DD)"
// @Success 200 {object} services.SalesReport
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/analytics/sales-report [get]
func (h *AnalyticsHandler) GetSalesReport(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Parse date parameters
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	if startDateStr == "" || endDateStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "start_date and end_date are required"})
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format. Use YYYY-MM-DD"})
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format. Use YYYY-MM-DD"})
		return
	}

	// Validate date range
	if endDate.Before(startDate) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "end_date must be after start_date"})
		return
	}

	report, err := h.analyticsService.GetSalesReport(c.Request.Context(), branchID, startDate, endDate)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate sales report")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate sales report"})
		return
	}

	c.JSON(http.StatusOK, report)
}

// GetPopularItems godoc
// @Summary Get popular menu items
// @Description Get the most popular menu items based on order frequency
// @Tags analytics
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param limit query int false "Number of items to return (default: 10)"
// @Param days query int false "Number of days to look back (default: 30)"
// @Success 200 {array} services.PopularItem
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/analytics/popular-items [get]
func (h *AnalyticsHandler) GetPopularItems(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Parse query parameters
	limit := 10
	if limitStr := c.Query("limit"); limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	days := 30
	if daysStr := c.Query("days"); daysStr != "" {
		if parsedDays, err := strconv.Atoi(daysStr); err == nil && parsedDays > 0 {
			days = parsedDays
		}
	}

	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -days)

	// This would use a method to get popular items
	// For now, we'll return an empty response
	popularItems := []services.PopularItem{}

	c.JSON(http.StatusOK, popularItems)
}

// GetCustomerAnalytics godoc
// @Summary Get customer analytics
// @Description Get customer behavior analytics and insights
// @Tags analytics
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param start_date query string false "Start date (YYYY-MM-DD)"
// @Param end_date query string false "End date (YYYY-MM-DD)"
// @Success 200 {object} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/analytics/customers [get]
func (h *AnalyticsHandler) GetCustomerAnalytics(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Parse optional date parameters
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -30) // Default to last 30 days

	if startDateStr := c.Query("start_date"); startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = parsed
		}
	}

	if endDateStr := c.Query("end_date"); endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = parsed
		}
	}

	// Placeholder for customer analytics
	analytics := gin.H{
		"period":           startDate.Format("2006-01-02") + " to " + endDate.Format("2006-01-02"),
		"total_customers":  0,
		"new_customers":    0,
		"returning_customers": 0,
		"customer_retention_rate": 0.0,
		"average_order_value": 0.0,
		"customer_segments": []gin.H{},
	}

	c.JSON(http.StatusOK, analytics)
}

// GetStaffPerformance godoc
// @Summary Get staff performance analytics
// @Description Get staff performance metrics and analytics
// @Tags analytics
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param start_date query string false "Start date (YYYY-MM-DD)"
// @Param end_date query string false "End date (YYYY-MM-DD)"
// @Success 200 {object} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/analytics/staff [get]
func (h *AnalyticsHandler) GetStaffPerformance(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Parse optional date parameters
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -30) // Default to last 30 days

	if startDateStr := c.Query("start_date"); startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = parsed
		}
	}

	if endDateStr := c.Query("end_date"); endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = parsed
		}
	}

	// Placeholder for staff performance analytics
	performance := gin.H{
		"period":              startDate.Format("2006-01-02") + " to " + endDate.Format("2006-01-02"),
		"total_staff":         0,
		"average_efficiency":  0.0,
		"top_performers":      []gin.H{},
		"performance_trends":  []gin.H{},
	}

	c.JSON(http.StatusOK, performance)
}

// GetTableUtilization godoc
// @Summary Get table utilization analytics
// @Description Get table utilization metrics and analytics
// @Tags analytics
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param date query string false "Date (YYYY-MM-DD, default: today)"
// @Success 200 {object} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/analytics/tables [get]
func (h *AnalyticsHandler) GetTableUtilization(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Parse date parameter
	date := time.Now()
	if dateStr := c.Query("date"); dateStr != "" {
		if parsed, err := time.Parse("2006-01-02", dateStr); err == nil {
			date = parsed
		}
	}

	// Placeholder for table utilization analytics
	utilization := gin.H{
		"date":                date.Format("2006-01-02"),
		"total_tables":        0,
		"average_utilization": 0.0,
		"peak_hours":          []gin.H{},
		"table_turnover":      0.0,
		"hourly_utilization":  []gin.H{},
	}

	c.JSON(http.StatusOK, utilization)
}

// ExportReport godoc
// @Summary Export analytics report
// @Description Export analytics report in various formats (PDF, Excel, CSV)
// @Tags analytics
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param report_type query string true "Report type (sales, customers, staff, tables)"
// @Param format query string true "Export format (pdf, excel, csv)"
// @Param start_date query string true "Start date (YYYY-MM-DD)"
// @Param end_date query string true "End date (YYYY-MM-DD)"
// @Success 200 {object} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/analytics/export [post]
func (h *AnalyticsHandler) ExportReport(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	reportType := c.Query("report_type")
	format := c.Query("format")
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	// Validate required parameters
	if reportType == "" || format == "" || startDateStr == "" || endDateStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "report_type, format, start_date, and end_date are required"})
		return
	}

	// Validate report type
	validReportTypes := []string{"sales", "customers", "staff", "tables"}
	isValidReportType := false
	for _, validType := range validReportTypes {
		if reportType == validType {
			isValidReportType = true
			break
		}
	}
	if !isValidReportType {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report_type. Must be one of: sales, customers, staff, tables"})
		return
	}

	// Validate format
	validFormats := []string{"pdf", "excel", "csv"}
	isValidFormat := false
	for _, validFmt := range validFormats {
		if format == validFmt {
			isValidFormat = true
			break
		}
	}
	if !isValidFormat {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid format. Must be one of: pdf, excel, csv"})
		return
	}

	// Parse dates
	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format. Use YYYY-MM-DD"})
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format. Use YYYY-MM-DD"})
		return
	}

	// For now, return a placeholder response
	// In a real implementation, this would generate and return the actual file
	exportInfo := gin.H{
		"message":     "Report export initiated",
		"report_type": reportType,
		"format":      format,
		"start_date":  startDate.Format("2006-01-02"),
		"end_date":    endDate.Format("2006-01-02"),
		"status":      "processing",
		"download_url": "", // Would contain the actual download URL
	}

	c.JSON(http.StatusOK, exportInfo)
}
