package handlers

import (
	"net/http"
	"strconv"

	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// OrderHandler handles order-related HTTP requests
type OrderHandler struct {
	orderService *services.OrderService
	logger       *logrus.Logger
}

// NewOrderHandler creates a new order handler
func NewOrderHandler(orderService *services.OrderService, logger *logrus.Logger) *OrderHandler {
	return &OrderHandler{
		orderService: orderService,
		logger:       logger,
	}
}

// GetOrders godoc
// @Summary Get orders
// @Description Get orders for a branch with optional filters
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param status query string false "Order status"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Success 200 {array} object
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/orders [get]
func (h *OrderHandler) GetOrders(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Parse query parameters
	status := c.Query("status")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	// Create filters
	filters := types.OrderFilters{
		Status: status,
		Page:   page,
		Limit:  limit,
	}

	orders, err := h.orderService.GetOrders(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.Error("Failed to get orders: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get orders"})
		return
	}

	c.JSON(http.StatusOK, orders)
}

// GetActiveOrders godoc
// @Summary Get active orders
// @Description Get all active orders for a branch
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Success 200 {array} object
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/orders/active [get]
func (h *OrderHandler) GetActiveOrders(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	orders, err := h.orderService.GetActiveOrders(c.Request.Context(), branchID)
	if err != nil {
		h.logger.Error("Failed to get active orders: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get active orders"})
		return
	}

	c.JSON(http.StatusOK, orders)
}

// GetCompletedOrders godoc
// @Summary Get completed orders
// @Description Get all completed orders for a branch
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Success 200 {array} object
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/orders/completed [get]
func (h *OrderHandler) GetCompletedOrders(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	orders, err := h.orderService.GetCompletedOrders(c.Request.Context(), branchID)
	if err != nil {
		h.logger.Error("Failed to get completed orders: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get completed orders"})
		return
	}

	c.JSON(http.StatusOK, orders)
}

// GetOrder godoc
// @Summary Get order by ID
// @Description Get a specific order by ID
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param orderId path string true "Order ID"
// @Success 200 {object} object
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/orders/{orderId} [get]
func (h *OrderHandler) GetOrder(c *gin.Context) {
	orderID, err := uuid.Parse(c.Param("orderId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	order, err := h.orderService.GetOrderByID(c.Request.Context(), orderID)
	if err != nil {
		h.logger.Error("Failed to get order: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get order"})
		return
	}

	if order == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Order not found"})
		return
	}

	c.JSON(http.StatusOK, order)
}

// CreateOrder godoc
// @Summary Create a new order
// @Description Create a new order
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param order body types.CreateOrderRequest true "Order data"
// @Success 201 {object} object
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/orders [post]
func (h *OrderHandler) CreateOrder(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	order, err := h.orderService.CreateOrder(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.Error("Failed to create order: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create order"})
		return
	}

	c.JSON(http.StatusCreated, order)
}

// UpdateOrder godoc
// @Summary Update an order
// @Description Update an existing order
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param orderId path string true "Order ID"
// @Param order body types.UpdateOrderRequest true "Order data"
// @Success 200 {object} object
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/orders/{orderId} [put]
func (h *OrderHandler) UpdateOrder(c *gin.Context) {
	orderID, err := uuid.Parse(c.Param("orderId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	var req types.UpdateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	order, err := h.orderService.UpdateOrder(c.Request.Context(), orderID, req)
	if err != nil {
		h.logger.Error("Failed to update order: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update order"})
		return
	}

	if order == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Order not found"})
		return
	}

	c.JSON(http.StatusOK, order)
}

// UpdateOrderStatus godoc
// @Summary Update order status
// @Description Update the status of an order
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param orderId path string true "Order ID"
// @Param status body map[string]string true "Status data"
// @Success 200 {object} object
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/orders/{orderId}/status [patch]
func (h *OrderHandler) UpdateOrderStatus(c *gin.Context) {
	orderID, err := uuid.Parse(c.Param("orderId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	order, err := h.orderService.UpdateOrderStatus(c.Request.Context(), orderID, req.Status)
	if err != nil {
		h.logger.Error("Failed to update order status: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update order status"})
		return
	}

	if order == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Order not found"})
		return
	}

	c.JSON(http.StatusOK, order)
}

// CancelOrder godoc
// @Summary Cancel an order
// @Description Cancel an existing order
// @Tags orders
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param orderId path string true "Order ID"
// @Success 200 {object} object
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/orders/{orderId} [delete]
func (h *OrderHandler) CancelOrder(c *gin.Context) {
	orderID, err := uuid.Parse(c.Param("orderId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	var req struct {
		Reason string `json:"reason"`
	}
	c.ShouldBindJSON(&req) // Optional body

	order, err := h.orderService.CancelOrder(c.Request.Context(), orderID, req.Reason)
	if err != nil {
		h.logger.Error("Failed to cancel order: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel order"})
		return
	}

	if order == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Order not found"})
		return
	}

	c.JSON(http.StatusOK, order)
}

// Placeholder methods for dashboard and reports
func (h *OrderHandler) GetDashboardStats(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Dashboard stats endpoint - to be implemented"})
}

func (h *OrderHandler) GetRecentActivity(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Recent activity endpoint - to be implemented"})
}

func (h *OrderHandler) GetSalesTrends(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Sales trends endpoint - to be implemented"})
}

func (h *OrderHandler) GetRevenueReport(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Revenue report endpoint - to be implemented"})
}
