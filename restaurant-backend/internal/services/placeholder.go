package services

import (
	"restaurant-backend/internal/repositories"

	"github.com/sirupsen/logrus"
)

// Placeholder services for entities that will be implemented later

// MerchantService handles merchant business logic
type MerchantService struct {
	merchantRepo repositories.MerchantRepository
	logger       *logrus.Logger
}

func NewMerchantService(merchantRepo repositories.MerchantRepository, logger *logrus.Logger) *MerchantService {
	return &MerchantService{merchantRepo: merchantRepo, logger: logger}
}

// BranchService handles branch business logic
type BranchService struct {
	branchRepo repositories.BranchRepository
	logger     *logrus.Logger
}

func NewBranchService(branchRepo repositories.BranchRepository, logger *logrus.Logger) *BranchService {
	return &BranchService{branchRepo: branchRepo, logger: logger}
}

// UserService handles user business logic
type UserService struct {
	userRepo repositories.UserRepository
	roleRepo repositories.RoleRepository
	logger   *logrus.Logger
}

func NewUserService(userRepo repositories.UserRepository, roleRepo repositories.RoleRepository, logger *logrus.Logger) *UserService {
	return &UserService{userRepo: userRepo, roleRepo: roleRepo, logger: logger}
}

// MenuService handles menu business logic
type MenuService struct {
	menuRepo repositories.MenuRepository
	logger   *logrus.Logger
}

func NewMenuService(menuRepo repositories.MenuRepository, logger *logrus.Logger) *MenuService {
	return &MenuService{menuRepo: menuRepo, logger: logger}
}

// ReservationService handles reservation business logic
type ReservationService struct {
	reservationRepo repositories.ReservationRepository
	logger          *logrus.Logger
}

func NewReservationService(reservationRepo repositories.ReservationRepository, logger *logrus.Logger) *ReservationService {
	return &ReservationService{reservationRepo: reservationRepo, logger: logger}
}

// TableService handles table business logic
type TableService struct {
	tableRepo repositories.TableRepository
	logger    *logrus.Logger
}

func NewTableService(tableRepo repositories.TableRepository, logger *logrus.Logger) *TableService {
	return &TableService{tableRepo: tableRepo, logger: logger}
}

// ReviewService handles review business logic
type ReviewService struct {
	reviewRepo repositories.ReviewRepository
	logger     *logrus.Logger
}

func NewReviewService(reviewRepo repositories.ReviewRepository, logger *logrus.Logger) *ReviewService {
	return &ReviewService{reviewRepo: reviewRepo, logger: logger}
}
