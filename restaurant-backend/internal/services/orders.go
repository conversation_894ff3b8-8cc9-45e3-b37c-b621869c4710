package services

import (
	"context"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// OrderService handles business logic for orders
type OrderService struct {
	orderRepo repositories.OrderRepository
	logger    *logrus.Logger
}

// NewOrderService creates a new order service
func NewOrderService(orderRepo repositories.OrderRepository, logger *logrus.Logger) *OrderService {
	return &OrderService{
		orderRepo: orderRepo,
		logger:    logger,
	}
}



// GetOrders retrieves orders with filters
func (s *OrderService) GetOrders(ctx context.Context, branchID uuid.UUID, filters types.OrderFilters) ([]*models.Order, error) {
	s.logger.WithFields(logrus.Fields{
		"branch_id": branchID,
		"filters":   filters,
	}).Info("Getting orders")

	return s.orderRepo.GetByBranch(ctx, branchID, filters)
}

// GetActiveOrders retrieves all active orders
func (s *OrderService) GetActiveOrders(ctx context.Context, branchID uuid.UUID) ([]*models.Order, error) {
	s.logger.WithFields(logrus.Fields{
		"branch_id": branchID,
	}).Info("Getting active orders")

	filters := types.OrderFilters{
		Status: "active", // This would be expanded to include multiple active statuses
	}

	return s.orderRepo.GetByBranch(ctx, branchID, filters)
}

// GetCompletedOrders retrieves all completed orders
func (s *OrderService) GetCompletedOrders(ctx context.Context, branchID uuid.UUID) ([]*models.Order, error) {
	s.logger.WithFields(logrus.Fields{
		"branch_id": branchID,
	}).Info("Getting completed orders")

	filters := types.OrderFilters{
		Status: models.OrderStatusCompleted,
	}

	return s.orderRepo.GetByBranch(ctx, branchID, filters)
}

// GetOrderByID retrieves an order by ID
func (s *OrderService) GetOrderByID(ctx context.Context, orderID uuid.UUID) (*models.Order, error) {
	s.logger.WithFields(logrus.Fields{
		"order_id": orderID,
	}).Info("Getting order by ID")

	return s.orderRepo.GetByID(ctx, orderID)
}

// CreateOrder creates a new order
func (s *OrderService) CreateOrder(ctx context.Context, branchID uuid.UUID, req types.CreateOrderRequest) (*models.Order, error) {
	s.logger.WithFields(logrus.Fields{
		"branch_id":     branchID,
		"customer_name": req.CustomerName,
		"type":          req.Type,
	}).Info("Creating new order")

	// Create order model
	order := &models.Order{
		BranchID:      branchID,
		CustomerName:  req.CustomerName,
		CustomerPhone: req.CustomerPhone,
		CustomerEmail: req.CustomerEmail,
		TableID:       req.TableID,
		Type:          req.Type,
		Status:        models.OrderStatusPending,
		Notes:         req.Notes,
	}

	// Create order items
	for _, itemReq := range req.Items {
		item := models.OrderItem{
			MenuItemID:    &itemReq.MenuItemID,
			Name:          itemReq.Name,
			Price:         itemReq.Price,
			Quantity:      itemReq.Quantity,
			Modifications: models.ModificationsData(itemReq.Modifications),
			Notes:         itemReq.Notes,
		}
		item.CalculateTotal()
		order.Items = append(order.Items, item)
	}

	// Calculate order totals
	order.CalculateTotal()

	// Save order
	if err := s.orderRepo.Create(ctx, order); err != nil {
		s.logger.WithError(err).Error("Failed to create order")
		return nil, err
	}

	s.logger.WithField("order_id", order.ID).Info("Order created successfully")
	return order, nil
}

// UpdateOrder updates an existing order
func (s *OrderService) UpdateOrder(ctx context.Context, orderID uuid.UUID, req types.UpdateOrderRequest) (*models.Order, error) {
	s.logger.WithFields(logrus.Fields{
		"order_id": orderID,
	}).Info("Updating order")

	// Get existing order
	order, err := s.orderRepo.GetByID(ctx, orderID)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, nil
	}

	// Check if order is editable
	if !order.IsEditable() {
		s.logger.WithField("order_status", order.Status).Warn("Order is not editable")
		return nil, ErrOrderNotEditable
	}

	// Update order fields
	if req.CustomerName != "" {
		order.CustomerName = req.CustomerName
	}
	if req.CustomerPhone != "" {
		order.CustomerPhone = req.CustomerPhone
	}
	if req.CustomerEmail != "" {
		order.CustomerEmail = req.CustomerEmail
	}
	if req.TableID != nil {
		order.TableID = req.TableID
	}
	if req.Notes != "" {
		order.Notes = req.Notes
	}

	// Update items if provided
	if len(req.Items) > 0 {
		// Clear existing items and add new ones
		order.Items = []models.OrderItem{}
		for _, itemReq := range req.Items {
			item := models.OrderItem{
				OrderID:       order.ID,
				MenuItemID:    &itemReq.MenuItemID,
				Name:          itemReq.Name,
				Price:         itemReq.Price,
				Quantity:      itemReq.Quantity,
				Modifications: models.ModificationsData(itemReq.Modifications),
				Notes:         itemReq.Notes,
			}
			item.CalculateTotal()
			order.Items = append(order.Items, item)
		}
	}

	// Recalculate totals
	order.CalculateTotal()

	// Save updated order
	if err := s.orderRepo.Update(ctx, order); err != nil {
		s.logger.WithError(err).Error("Failed to update order")
		return nil, err
	}

	s.logger.Info("Order updated successfully")
	return order, nil
}

// UpdateOrderStatus updates the status of an order
func (s *OrderService) UpdateOrderStatus(ctx context.Context, orderID uuid.UUID, status string) (*models.Order, error) {
	s.logger.WithFields(logrus.Fields{
		"order_id": orderID,
		"status":   status,
	}).Info("Updating order status")

	// Get existing order
	order, err := s.orderRepo.GetByID(ctx, orderID)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, nil
	}

	// Validate status transition
	if !s.isValidStatusTransition(order.Status, status) {
		s.logger.WithFields(logrus.Fields{
			"current_status": order.Status,
			"new_status":     status,
		}).Warn("Invalid status transition")
		return nil, ErrInvalidStatusTransition
	}

	// Update status and related timestamps
	order.Status = status
	now := time.Now()

	switch status {
	case models.OrderStatusServed:
		order.ServedAt = &now
	case models.OrderStatusCompleted:
		order.CompletedAt = &now
	case models.OrderStatusCancelled:
		order.CancelledAt = &now
	}

	// Save updated order
	if err := s.orderRepo.UpdateStatus(ctx, orderID, status); err != nil {
		s.logger.WithError(err).Error("Failed to update order status")
		return nil, err
	}

	s.logger.Info("Order status updated successfully")
	return order, nil
}

// CancelOrder cancels an order
func (s *OrderService) CancelOrder(ctx context.Context, orderID uuid.UUID, reason string) (*models.Order, error) {
	s.logger.WithFields(logrus.Fields{
		"order_id": orderID,
		"reason":   reason,
	}).Info("Cancelling order")

	// Get existing order
	order, err := s.orderRepo.GetByID(ctx, orderID)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, nil
	}

	// Check if order can be cancelled
	if !order.IsCancellable() {
		s.logger.WithField("order_status", order.Status).Warn("Order cannot be cancelled")
		return nil, ErrOrderNotCancellable
	}

	// Update order
	now := time.Now()
	order.Status = models.OrderStatusCancelled
	order.CancelledAt = &now
	order.CancellationReason = reason

	// Save updated order
	if err := s.orderRepo.Update(ctx, order); err != nil {
		s.logger.WithError(err).Error("Failed to cancel order")
		return nil, err
	}

	s.logger.Info("Order cancelled successfully")
	return order, nil
}

// Helper methods

func (s *OrderService) isValidStatusTransition(currentStatus, newStatus string) bool {
	validTransitions := map[string][]string{
		models.OrderStatusPending:   {models.OrderStatusConfirmed, models.OrderStatusCancelled},
		models.OrderStatusConfirmed: {models.OrderStatusPreparing, models.OrderStatusCancelled},
		models.OrderStatusPreparing: {models.OrderStatusReady, models.OrderStatusCancelled},
		models.OrderStatusReady:     {models.OrderStatusServed, models.OrderStatusCancelled},
		models.OrderStatusServed:    {models.OrderStatusCompleted},
		models.OrderStatusCompleted: {}, // No transitions from completed
		models.OrderStatusCancelled: {}, // No transitions from cancelled
	}

	allowedStatuses, exists := validTransitions[currentStatus]
	if !exists {
		return false
	}

	for _, status := range allowedStatuses {
		if status == newStatus {
			return true
		}
	}

	return false
}

// Custom errors
var (
	ErrOrderNotEditable         = NewServiceError("ORDER_NOT_EDITABLE", "Order is not editable in current status")
	ErrOrderNotCancellable      = NewServiceError("ORDER_NOT_CANCELLABLE", "Order cannot be cancelled in current status")
	ErrInvalidStatusTransition  = NewServiceError("INVALID_STATUS_TRANSITION", "Invalid status transition")
)

// ServiceError represents a service-level error
type ServiceError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

func (e ServiceError) Error() string {
	return e.Message
}

// NewServiceError creates a new service error
func NewServiceError(code, message string) ServiceError {
	return ServiceError{
		Code:    code,
		Message: message,
	}
}
