package repositories

import (
	"context"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// OrderRepository defines the interface for order data access
type OrderRepository interface {
	Create(ctx context.Context, order *models.Order) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.Order, error)
	GetByBranch(ctx context.Context, branchID uuid.UUID, filters types.OrderFilters) ([]*models.Order, error)
	Update(ctx context.Context, order *models.Order) error
	UpdateStatus(ctx context.Context, id uuid.UUID, status string) error
	Delete(ctx context.Context, id uuid.UUID) error
}

// orderRepository implements OrderRepository
type orderRepository struct {
	db *gorm.DB
}

// NewOrderRepository creates a new order repository
func NewOrderRepository(db *gorm.DB) OrderRepository {
	return &orderRepository{db: db}
}

// Create creates a new order
func (r *orderRepository) Create(ctx context.Context, order *models.Order) error {
	return r.db.WithContext(ctx).Create(order).Error
}

// GetByID retrieves an order by ID
func (r *orderRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Order, error) {
	var order models.Order
	err := r.db.WithContext(ctx).
		Preload("Items").
		Preload("Table").
		Preload("Payments").
		First(&order, id).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &order, nil
}

// GetByBranch retrieves orders for a branch with filters
func (r *orderRepository) GetByBranch(ctx context.Context, branchID uuid.UUID, filters types.OrderFilters) ([]*models.Order, error) {
	query := r.db.WithContext(ctx).
		Preload("Items").
		Preload("Table").
		Where("branch_id = ?", branchID)

	// Apply filters
	if filters.Status != "" {
		if filters.Status == "active" {
			// Active orders include pending, confirmed, preparing, ready, served
			query = query.Where("status IN ?", []string{
				models.OrderStatusPending,
				models.OrderStatusConfirmed,
				models.OrderStatusPreparing,
				models.OrderStatusReady,
				models.OrderStatusServed,
			})
		} else {
			query = query.Where("status = ?", filters.Status)
		}
	}

	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}

	// Apply pagination
	if filters.Page > 0 && filters.Limit > 0 {
		offset := (filters.Page - 1) * filters.Limit
		query = query.Offset(offset).Limit(filters.Limit)
	}

	// Order by creation time (newest first)
	query = query.Order("created_at DESC")

	var orders []*models.Order
	err := query.Find(&orders).Error
	if err != nil {
		return nil, err
	}

	return orders, nil
}

// Update updates an existing order
func (r *orderRepository) Update(ctx context.Context, order *models.Order) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Update the order
		if err := tx.Save(order).Error; err != nil {
			return err
		}

		// Delete existing items
		if err := tx.Where("order_id = ?", order.ID).Delete(&models.OrderItem{}).Error; err != nil {
			return err
		}

		// Create new items
		for i := range order.Items {
			order.Items[i].OrderID = order.ID
			if err := tx.Create(&order.Items[i]).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// UpdateStatus updates the status of an order
func (r *orderRepository) UpdateStatus(ctx context.Context, id uuid.UUID, status string) error {
	return r.db.WithContext(ctx).
		Model(&models.Order{}).
		Where("id = ?", id).
		Update("status", status).Error
}

// Delete deletes an order (soft delete)
func (r *orderRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.Order{}, id).Error
}
