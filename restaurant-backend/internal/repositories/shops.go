package repositories

import (
	"context"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type ShopRepository struct {
	db *gorm.DB
}

func NewShopRepository(db *gorm.DB) *ShopRepository {
	return &ShopRepository{db: db}
}

// Shop methods
func (r *ShopRepository) GetShops(ctx context.Context, filters types.ShopFilters) ([]models.Shop, int64, error) {
	var shops []models.Shop
	var total int64

	query := r.db.WithContext(ctx).Model(&models.Shop{})

	// Apply filters
	if filters.ShopType != "" {
		query = query.Where("shop_type = ?", filters.ShopType)
	}
	if filters.CuisineType != "" {
		query = query.Where("cuisine_type = ?", filters.CuisineType)
	}
	if filters.PriceRange != "" {
		query = query.Where("price_range = ?", filters.PriceRange)
	}
	if filters.City != "" {
		query = query.Where("address_city = ?", filters.City)
	}
	if filters.State != "" {
		query = query.Where("address_state = ?", filters.State)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.IsActive != nil {
		query = query.Where("is_active = ?", *filters.IsActive)
	}
	if filters.Search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ? OR cuisine_type ILIKE ?",
			"%"+filters.Search+"%", "%"+filters.Search+"%", "%"+filters.Search+"%")
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	if err := query.Offset(offset).Limit(filters.Limit).
		Order("name").
		Find(&shops).Error; err != nil {
		return nil, 0, err
	}

	return shops, total, nil
}

func (r *ShopRepository) GetShopByID(ctx context.Context, shopID uuid.UUID) (*models.Shop, error) {
	var shop models.Shop
	if err := r.db.WithContext(ctx).Preload("Branches").
		First(&shop, "id = ?", shopID).Error; err != nil {
		return nil, err
	}
	return &shop, nil
}

func (r *ShopRepository) GetShopBySlug(ctx context.Context, slug string) (*models.Shop, error) {
	var shop models.Shop
	if err := r.db.WithContext(ctx).Preload("Branches").
		First(&shop, "slug = ?", slug).Error; err != nil {
		return nil, err
	}
	return &shop, nil
}

func (r *ShopRepository) GetShopsByOwner(ctx context.Context, ownerID uuid.UUID) ([]models.Shop, error) {
	var shops []models.Shop
	if err := r.db.WithContext(ctx).Preload("Branches").
		Where("owner_id = ?", ownerID).
		Order("name").
		Find(&shops).Error; err != nil {
		return nil, err
	}
	return shops, nil
}

func (r *ShopRepository) GetShopsByType(ctx context.Context, shopType string) ([]models.Shop, error) {
	var shops []models.Shop
	if err := r.db.WithContext(ctx).Preload("Branches").
		Where("shop_type = ? AND is_active = ?", shopType, true).
		Order("name").
		Find(&shops).Error; err != nil {
		return nil, err
	}
	return shops, nil
}

func (r *ShopRepository) CreateShop(ctx context.Context, shop *models.Shop) error {
	return r.db.WithContext(ctx).Create(shop).Error
}

func (r *ShopRepository) UpdateShop(ctx context.Context, shopID uuid.UUID, updates map[string]interface{}) (*models.Shop, error) {
	var shop models.Shop
	if err := r.db.WithContext(ctx).First(&shop, "id = ?", shopID).Error; err != nil {
		return nil, err
	}

	if err := r.db.WithContext(ctx).Model(&shop).Updates(updates).Error; err != nil {
		return nil, err
	}

	// Reload with relationships
	if err := r.db.WithContext(ctx).Preload("Branches").
		First(&shop, "id = ?", shopID).Error; err != nil {
		return nil, err
	}

	return &shop, nil
}

func (r *ShopRepository) DeleteShop(ctx context.Context, shopID uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.Shop{}, "id = ?", shopID).Error
}

// Branch methods
func (r *ShopRepository) GetBranches(ctx context.Context, shopID uuid.UUID, filters types.BranchFilters) ([]models.ShopBranch, int64, error) {
	var branches []models.ShopBranch
	var total int64

	query := r.db.WithContext(ctx).Model(&models.ShopBranch{}).Where("shop_id = ?", shopID)

	// Apply filters
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.IsActive != nil {
		query = query.Where("is_active = ?", *filters.IsActive)
	}
	if filters.City != "" {
		query = query.Where("address_city = ?", filters.City)
	}
	if filters.State != "" {
		query = query.Where("address_state = ?", filters.State)
	}
	if filters.Search != "" {
		query = query.Where("name ILIKE ? OR address_street ILIKE ?",
			"%"+filters.Search+"%", "%"+filters.Search+"%")
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	if err := query.Offset(offset).Limit(filters.Limit).
		Order("name").
		Find(&branches).Error; err != nil {
		return nil, 0, err
	}

	return branches, total, nil
}

func (r *ShopRepository) GetBranchByID(ctx context.Context, shopID, branchID uuid.UUID) (*models.ShopBranch, error) {
	var branch models.ShopBranch
	if err := r.db.WithContext(ctx).Preload("Shop").
		First(&branch, "id = ? AND shop_id = ?", branchID, shopID).Error; err != nil {
		return nil, err
	}
	return &branch, nil
}

func (r *ShopRepository) GetBranchBySlug(ctx context.Context, shopSlug, branchSlug string) (*models.ShopBranch, error) {
	var branch models.ShopBranch
	if err := r.db.WithContext(ctx).
		Joins("JOIN shops ON shop_branches.shop_id = shops.id").
		Preload("Shop").
		Where("shops.slug = ? AND shop_branches.slug = ?", shopSlug, branchSlug).
		First(&branch).Error; err != nil {
		return nil, err
	}
	return &branch, nil
}

func (r *ShopRepository) CreateBranch(ctx context.Context, branch *models.ShopBranch) error {
	return r.db.WithContext(ctx).Create(branch).Error
}

func (r *ShopRepository) UpdateBranch(ctx context.Context, branchID uuid.UUID, updates map[string]interface{}) (*models.ShopBranch, error) {
	var branch models.ShopBranch
	if err := r.db.WithContext(ctx).First(&branch, "id = ?", branchID).Error; err != nil {
		return nil, err
	}

	if err := r.db.WithContext(ctx).Model(&branch).Updates(updates).Error; err != nil {
		return nil, err
	}

	// Reload with relationships
	if err := r.db.WithContext(ctx).Preload("Shop").
		First(&branch, "id = ?", branchID).Error; err != nil {
		return nil, err
	}

	return &branch, nil
}

func (r *ShopRepository) DeleteBranch(ctx context.Context, branchID uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.ShopBranch{}, "id = ?", branchID).Error
}

// Combined methods
func (r *ShopRepository) GetShopsWithBranches(ctx context.Context, filters types.ShopFilters) ([]models.Shop, int64, error) {
	var shops []models.Shop
	var total int64

	query := r.db.WithContext(ctx).Model(&models.Shop{})

	// Apply filters (same as GetShops)
	if filters.ShopType != "" {
		query = query.Where("shop_type = ?", filters.ShopType)
	}
	if filters.CuisineType != "" {
		query = query.Where("cuisine_type = ?", filters.CuisineType)
	}
	if filters.PriceRange != "" {
		query = query.Where("price_range = ?", filters.PriceRange)
	}
	if filters.City != "" {
		query = query.Where("address_city = ?", filters.City)
	}
	if filters.State != "" {
		query = query.Where("address_state = ?", filters.State)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.IsActive != nil {
		query = query.Where("is_active = ?", *filters.IsActive)
	}
	if filters.Search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ? OR cuisine_type ILIKE ?",
			"%"+filters.Search+"%", "%"+filters.Search+"%", "%"+filters.Search+"%")
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and preload branches
	offset := (filters.Page - 1) * filters.Limit
	if err := query.Preload("Branches").
		Offset(offset).Limit(filters.Limit).
		Order("name").
		Find(&shops).Error; err != nil {
		return nil, 0, err
	}

	return shops, total, nil
}

func (r *ShopRepository) GetBranchWithShop(ctx context.Context, branchID uuid.UUID) (*models.ShopBranch, error) {
	var branch models.ShopBranch
	if err := r.db.WithContext(ctx).Preload("Shop").
		First(&branch, "id = ?", branchID).Error; err != nil {
		return nil, err
	}
	return &branch, nil
}

// Settings methods
func (r *ShopRepository) GetBranchSettings(ctx context.Context, branchID uuid.UUID) (*models.BranchSettings, error) {
	var branch models.ShopBranch
	if err := r.db.WithContext(ctx).Select("settings").
		First(&branch, "id = ?", branchID).Error; err != nil {
		return nil, err
	}
	return &branch.Settings, nil
}

func (r *ShopRepository) UpdateBranchSettings(ctx context.Context, branchID uuid.UUID, settings models.BranchSettings) error {
	return r.db.WithContext(ctx).Model(&models.ShopBranch{}).
		Where("id = ?", branchID).
		Update("settings", settings).Error
}
