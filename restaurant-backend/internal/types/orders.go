package types

import (
	"restaurant-backend/internal/models"

	"github.com/google/uuid"
)

// OrderFilters represents filters for order queries
type OrderFilters struct {
	Status string
	Type   string
	Page   int
	Limit  int
}

// CreateOrderRequest represents the request to create an order
type CreateOrderRequest struct {
	CustomerName  string                 `json:"customer_name" binding:"required"`
	CustomerPhone string                 `json:"customer_phone"`
	CustomerEmail string                 `json:"customer_email"`
	TableID       *uuid.UUID             `json:"table_id"`
	Type          string                 `json:"type" binding:"required"`
	Items         []CreateOrderItemRequest `json:"items" binding:"required,min=1"`
	Notes         string                 `json:"notes"`
}

// CreateOrderItemRequest represents an item in the order creation request
type CreateOrderItemRequest struct {
	MenuItemID    uuid.UUID                    `json:"menu_item_id" binding:"required"`
	Name          string                       `json:"name" binding:"required"`
	Price         float64                      `json:"price" binding:"required"`
	Quantity      int                          `json:"quantity" binding:"required,min=1"`
	Modifications []models.OrderModification   `json:"modifications"`
	Notes         string                       `json:"notes"`
}

// UpdateOrderRequest represents the request to update an order
type UpdateOrderRequest struct {
	CustomerName  string                 `json:"customer_name"`
	CustomerPhone string                 `json:"customer_phone"`
	CustomerEmail string                 `json:"customer_email"`
	TableID       *uuid.UUID             `json:"table_id"`
	Items         []CreateOrderItemRequest `json:"items"`
	Notes         string                 `json:"notes"`
}
