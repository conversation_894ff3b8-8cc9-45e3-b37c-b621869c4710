package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"restaurant-backend/internal/api/routes"
	"restaurant-backend/internal/config"
	"restaurant-backend/internal/database"
	"restaurant-backend/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

// @title Restaurant Management API
// @version 1.0
// @description API for restaurant management system
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.restaurant-api.com/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// Load environment variables from .env file if it exists
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logger
	logger := logger.NewLogger(cfg.Log.Level, cfg.Log.Format)
	logger.Info("Starting Restaurant Management API")

	// Initialize database
	db, err := database.Initialize(cfg.GetDSN(), logger)
	if err != nil {
		logger.Fatalf("Failed to initialize database: %v", err)
	}

	// Run database migrations
	if err := database.Migrate(db); err != nil {
		logger.Fatalf("Failed to run migrations: %v", err)
	}

	// Initialize Redis (if needed)
	// redis := redis.NewClient(&redis.Options{
	//     Addr:     cfg.GetRedisAddr(),
	//     Password: cfg.Redis.Password,
	//     DB:       cfg.Redis.DB,
	// })

	// Set Gin mode
	gin.SetMode(cfg.Server.GinMode)

	// Initialize Gin router
	router := gin.New()

	// Setup routes
	routes.SetupRoutes(router, db, cfg, logger)

	// Create HTTP server
	server := &http.Server{
		Addr:         ":" + cfg.Server.Port,
		Handler:      router,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Start server in a goroutine
	go func() {
		logger.Infof("Server starting on port %s", cfg.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// Give outstanding requests a deadline for completion
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown server
	if err := server.Shutdown(ctx); err != nil {
		logger.Fatalf("Server forced to shutdown: %v", err)
	}

	// Close database connection
	sqlDB, err := db.DB()
	if err == nil {
		sqlDB.Close()
	}

	logger.Info("Server exited")
}

// Health check handler for Docker health checks
func init() {
	if len(os.Args) > 1 && os.Args[1] == "health" {
		// Simple health check
		resp, err := http.Get("http://localhost:8080/health")
		if err != nil || resp.StatusCode != http.StatusOK {
			os.Exit(1)
		}
		fmt.Println("Health check passed")
		os.Exit(0)
	}
}
