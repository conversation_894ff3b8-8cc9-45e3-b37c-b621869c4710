package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Merchant represents a restaurant chain or single restaurant owner
type Merchant struct {
	BaseModel
	Name             string    `json:"name" gorm:"type:varchar(255);not null"`
	Slug             string    `json:"slug" gorm:"type:varchar(255);uniqueIndex;not null"`
	Email            string    `json:"email" gorm:"type:varchar(255);uniqueIndex;not null"`
	Phone            string    `json:"phone" gorm:"type:varchar(50)"`
	Address          Address   `json:"address" gorm:"embedded;embeddedPrefix:address_"`
	Settings         Settings  `json:"settings" gorm:"type:jsonb"`
	SubscriptionPlan string    `json:"subscription_plan" gorm:"type:varchar(50);default:'basic'"`
	IsActive         bool      `json:"is_active" gorm:"default:true"`
	
	// Relationships
	Branches []Branch `json:"branches,omitempty" gorm:"foreignKey:MerchantID;constraint:OnDelete:CASCADE"`
	Users    []User   `json:"users,omitempty" gorm:"foreignKey:MerchantID;constraint:OnDelete:CASCADE"`
	Roles    []Role   `json:"roles,omitempty" gorm:"foreignKey:MerchantID;constraint:OnDelete:CASCADE"`
}

// Branch represents a single restaurant location
type Branch struct {
	BaseModel
	MerchantID uuid.UUID `json:"merchant_id" gorm:"type:uuid;not null;index"`
	Name       string    `json:"name" gorm:"type:varchar(255);not null"`
	Slug       string    `json:"slug" gorm:"type:varchar(255);not null"`
	Email      string    `json:"email" gorm:"type:varchar(255)"`
	Phone      string    `json:"phone" gorm:"type:varchar(50)"`
	Address    Address   `json:"address" gorm:"embedded;embeddedPrefix:address_"`
	Settings   Settings  `json:"settings" gorm:"type:jsonb"`
	Timezone   string    `json:"timezone" gorm:"type:varchar(50);default:'UTC'"`
	IsActive   bool      `json:"is_active" gorm:"default:true"`

	// Relationships
	Merchant        Merchant        `json:"merchant,omitempty" gorm:"foreignKey:MerchantID"`
	Users           []User          `json:"users,omitempty" gorm:"foreignKey:BranchID;constraint:OnDelete:CASCADE"`
	MenuCategories  []MenuCategory  `json:"menu_categories,omitempty" gorm:"foreignKey:BranchID;constraint:OnDelete:CASCADE"`
	MenuItems       []MenuItem      `json:"menu_items,omitempty" gorm:"foreignKey:BranchID;constraint:OnDelete:CASCADE"`
	Orders          []Order         `json:"orders,omitempty" gorm:"foreignKey:BranchID;constraint:OnDelete:CASCADE"`
	Reservations    []Reservation   `json:"reservations,omitempty" gorm:"foreignKey:BranchID;constraint:OnDelete:CASCADE"`
	Tables          []Table         `json:"tables,omitempty" gorm:"foreignKey:BranchID;constraint:OnDelete:CASCADE"`
	TableAreas      []TableArea     `json:"table_areas,omitempty" gorm:"foreignKey:BranchID;constraint:OnDelete:CASCADE"`
	Reviews         []Review        `json:"reviews,omitempty" gorm:"foreignKey:BranchID;constraint:OnDelete:CASCADE"`
}

// Settings represents configurable settings for merchants and branches
type Settings struct {
	Currency              string            `json:"currency"`
	TaxRate              float64           `json:"tax_rate"`
	ServiceChargeRate    float64           `json:"service_charge_rate"`
	DefaultTipPercentage float64           `json:"default_tip_percentage"`
	BusinessHours        map[string]string `json:"business_hours"`
	PaymentMethods       []string          `json:"payment_methods"`
	Features             map[string]bool   `json:"features"`
	Notifications        map[string]bool   `json:"notifications"`
	Theme                map[string]string `json:"theme"`
}

// Scan implements the sql.Scanner interface for Settings
func (s *Settings) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, s)
}

// Value implements the driver.Valuer interface for Settings
func (s Settings) Value() (driver.Value, error) {
	return json.Marshal(s)
}

// TableName specifies the table name for Merchant
func (Merchant) TableName() string {
	return "merchants"
}

// TableName specifies the table name for Branch
func (Branch) TableName() string {
	return "branches"
}

// BeforeCreate hook for Merchant
func (m *Merchant) BeforeCreate(tx *gorm.DB) error {
	if err := m.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}

	// Set default settings if not provided
	if m.Settings.Currency == "" {
		m.Settings = Settings{
			Currency:              "USD",
			TaxRate:              0.08,
			ServiceChargeRate:    0.0,
			DefaultTipPercentage: 15.0,
			BusinessHours: map[string]string{
				"monday":    "09:00-22:00",
				"tuesday":   "09:00-22:00",
				"wednesday": "09:00-22:00",
				"thursday":  "09:00-22:00",
				"friday":    "09:00-23:00",
				"saturday":  "09:00-23:00",
				"sunday":    "10:00-21:00",
			},
			PaymentMethods: []string{"cash", "credit_card", "debit_card"},
			Features: map[string]bool{
				"online_ordering":    true,
				"table_reservations": true,
				"qr_menu":           true,
				"reviews":           true,
				"analytics":         true,
			},
			Notifications: map[string]bool{
				"new_orders":      true,
				"reservations":    true,
				"reviews":         true,
				"low_inventory":   false,
				"staff_alerts":    true,
			},
			Theme: map[string]string{
				"primary_color":   "#e58219",
				"secondary_color": "#8a745c",
				"background":      "#fbfaf9",
				"text":           "#181510",
			},
		}
	}

	return nil
}

// BeforeCreate hook for Branch
func (b *Branch) BeforeCreate(tx *gorm.DB) error {
	if err := b.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}

	// Inherit settings from merchant if not provided
	if b.Settings.Currency == "" {
		var merchant Merchant
		if err := tx.First(&merchant, b.MerchantID).Error; err == nil {
			b.Settings = merchant.Settings
		}
	}

	return nil
}

// GetFullAddress returns the formatted full address
func (m *Merchant) GetFullAddress() string {
	addr := m.Address
	if addr.Street == "" {
		return ""
	}
	
	result := addr.Street
	if addr.City != "" {
		result += ", " + addr.City
	}
	if addr.State != "" {
		result += ", " + addr.State
	}
	if addr.ZipCode != "" {
		result += " " + addr.ZipCode
	}
	if addr.Country != "" {
		result += ", " + addr.Country
	}
	
	return result
}

// GetFullAddress returns the formatted full address for Branch
func (b *Branch) GetFullAddress() string {
	addr := b.Address
	if addr.Street == "" {
		return ""
	}
	
	result := addr.Street
	if addr.City != "" {
		result += ", " + addr.City
	}
	if addr.State != "" {
		result += ", " + addr.State
	}
	if addr.ZipCode != "" {
		result += " " + addr.ZipCode
	}
	if addr.Country != "" {
		result += ", " + addr.Country
	}
	
	return result
}

// IsFeatureEnabled checks if a feature is enabled
func (s *Settings) IsFeatureEnabled(feature string) bool {
	if s.Features == nil {
		return false
	}
	return s.Features[feature]
}

// IsNotificationEnabled checks if a notification type is enabled
func (s *Settings) IsNotificationEnabled(notification string) bool {
	if s.Notifications == nil {
		return false
	}
	return s.Notifications[notification]
}

// GetBusinessHours returns business hours for a specific day
func (s *Settings) GetBusinessHours(day string) string {
	if s.BusinessHours == nil {
		return ""
	}
	return s.BusinessHours[day]
}

// IsPaymentMethodAccepted checks if a payment method is accepted
func (s *Settings) IsPaymentMethodAccepted(method string) bool {
	if s.PaymentMethods == nil {
		return false
	}
	for _, pm := range s.PaymentMethods {
		if pm == method {
			return true
		}
	}
	return false
}
