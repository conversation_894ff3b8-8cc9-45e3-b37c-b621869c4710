/**
 * RTK Query API endpoints for merchants/restaurants
 * Handles all merchant-related API calls
 */

import { apiSlice } from '../apiSlice';

export interface Merchant {
  id: string;
  name: string;
  slug: string;
  description?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  timezone: string;
  currency: string;
  status: 'active' | 'inactive' | 'suspended';
  logo?: string;
  openingHours?: any;
  settings?: any;
  branches?: Array<{
    id: string;
    name: string;
    slug: string;
    address?: string;
    phone?: string;
    email?: string;
    status: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface MerchantFilters {
  status?: string;
  search?: string;
  city?: string;
  country?: string;
  page?: number;
  limit?: number;
}

export interface CreateMerchantRequest {
  name: string;
  description?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  timezone?: string;
  currency?: string;
  logo?: string;
  openingHours?: any;
  settings?: any;
}

export interface UpdateMerchantRequest {
  id: string;
  name?: string;
  description?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  timezone?: string;
  currency?: string;
  status?: string;
  logo?: string;
  openingHours?: any;
  settings?: any;
}

export interface MerchantsResponse {
  data: Merchant[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export const merchantApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get merchants with filters and pagination
    getMerchants: builder.query<MerchantsResponse, MerchantFilters>({
      query: (filters) => {
        const params = new URLSearchParams();

        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result) => [
        'Merchants',
        ...(result?.data || []).map(({ id }) => ({ type: 'Merchants' as const, id })),
      ],
    }),

    // Get single merchant
    getMerchant: builder.query<Merchant, string>({
      query: (merchantId) => ({
        url: `/merchants/${merchantId}`,
        method: 'GET',
      }),
      providesTags: (result, error, merchantId) => [
        { type: 'Merchants', id: merchantId },
      ],
    }),

    // Get merchant by slug
    getMerchantBySlug: builder.query<Merchant, string>({
      query: (slug) => ({
        url: `/merchants/slug/${slug}`,
        method: 'GET',
      }),
      providesTags: (result, error, slug) => [
        { type: 'Merchants', id: slug },
      ],
    }),

    // Create new merchant
    createMerchant: builder.mutation<Merchant, CreateMerchantRequest>({
      query: (merchantData) => ({
        url: '/merchants',
        method: 'POST',
        body: merchantData,
      }),
      invalidatesTags: ['Merchants'],
    }),

    // Update merchant
    updateMerchant: builder.mutation<Merchant, UpdateMerchantRequest>({
      query: ({ id, ...updates }) => ({
        url: `/merchants/${id}`,
        method: 'PUT',
        body: updates,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Merchants', id },
        'Merchants',
      ],
    }),

    // Delete merchant
    deleteMerchant: builder.mutation<void, string>({
      query: (merchantId) => ({
        url: `/merchants/${merchantId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, merchantId) => [
        { type: 'Merchants', id: merchantId },
        'Merchants',
      ],
    }),
  }),
});

export const {
  useGetMerchantsQuery,
  useGetMerchantQuery,
  useGetMerchantBySlugQuery,
  useCreateMerchantMutation,
  useUpdateMerchantMutation,
  useDeleteMerchantMutation,
} = merchantApi;
