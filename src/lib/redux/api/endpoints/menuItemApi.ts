/**
 * RTK Query API endpoints for menu items
 * Handles all menu item-related API calls
 */

import { apiSlice } from '../apiSlice';

export interface MenuItem {
  id: string;
  merchantId: string;
  name: string;
  slug: string;
  description?: string;
  price: number;
  category: string;
  image?: string;
  images?: string[];
  available: boolean;
  featured: boolean;
  sortOrder: number;
  calories?: number;
  ingredients?: string[];
  allergens?: string[];
  dietaryInfo?: string[];
  prepTime?: number;
  cookingTime?: number;
  createdAt: string;
  updatedAt: string;
}

export interface MenuItemFilters {
  category?: string;
  available?: boolean;
  featured?: boolean;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  page?: number;
  limit?: number;
}

export interface CreateMenuItemRequest {
  merchantId: string;
  name: string;
  description?: string;
  price: number;
  category: string;
  image?: string;
  images?: string[];
  available?: boolean;
  featured?: boolean;
  sortOrder?: number;
  calories?: number;
  ingredients?: string[];
  allergens?: string[];
  dietaryInfo?: string[];
  prepTime?: number;
  cookingTime?: number;
}

export interface UpdateMenuItemRequest {
  merchantId: string;
  menuItemId: string;
  name?: string;
  description?: string;
  price?: number;
  category?: string;
  image?: string;
  images?: string[];
  available?: boolean;
  featured?: boolean;
  sortOrder?: number;
  calories?: number;
  ingredients?: string[];
  allergens?: string[];
  dietaryInfo?: string[];
  prepTime?: number;
  cookingTime?: number;
}

export interface MenuItemsResponse {
  data: MenuItem[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface MenuCategoriesResponse {
  categories: string[];
}

export const menuItemApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get menu items with filters and pagination
    getMenuItems: builder.query<MenuItemsResponse, MenuItemFilters & { merchantId: string }>({
      query: ({ merchantId, ...filters }) => {
        const params = new URLSearchParams();
        
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/menu-items?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result, error, { merchantId }) => [
        { type: 'MenuItems', id: merchantId },
        ...(result?.data || []).map(({ id }) => ({ type: 'MenuItems' as const, id })),
      ],
    }),

    // Get single menu item
    getMenuItem: builder.query<MenuItem, { merchantId: string; menuItemId: string }>({
      query: ({ merchantId, menuItemId }) => ({
        url: `/merchants/${merchantId}/menu-items/${menuItemId}`,
        method: 'GET',
      }),
      providesTags: (result, error, { menuItemId }) => [
        { type: 'MenuItems', id: menuItemId },
      ],
    }),

    // Get menu item by slug
    getMenuItemBySlug: builder.query<MenuItem, { merchantId: string; slug: string }>({
      query: ({ merchantId, slug }) => ({
        url: `/merchants/${merchantId}/menu-items/slug/${slug}`,
        method: 'GET',
      }),
      providesTags: (result, error, { slug }) => [
        { type: 'MenuItems', id: slug },
      ],
    }),

    // Create new menu item
    createMenuItem: builder.mutation<MenuItem, CreateMenuItemRequest>({
      query: ({ merchantId, ...menuItemData }) => ({
        url: `/merchants/${merchantId}/menu-items`,
        method: 'POST',
        body: menuItemData,
      }),
      invalidatesTags: (result, error, { merchantId }) => [
        { type: 'MenuItems', id: merchantId },
      ],
    }),

    // Update menu item
    updateMenuItem: builder.mutation<MenuItem, UpdateMenuItemRequest>({
      query: ({ merchantId, menuItemId, ...updates }) => ({
        url: `/merchants/${merchantId}/menu-items/${menuItemId}`,
        method: 'PUT',
        body: updates,
      }),
      invalidatesTags: (result, error, { merchantId, menuItemId }) => [
        { type: 'MenuItems', id: menuItemId },
        { type: 'MenuItems', id: merchantId },
      ],
    }),

    // Delete menu item
    deleteMenuItem: builder.mutation<void, { merchantId: string; menuItemId: string }>({
      query: ({ merchantId, menuItemId }) => ({
        url: `/merchants/${merchantId}/menu-items/${menuItemId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { merchantId, menuItemId }) => [
        { type: 'MenuItems', id: menuItemId },
        { type: 'MenuItems', id: merchantId },
      ],
    }),

    // Update menu item availability
    updateMenuItemAvailability: builder.mutation<MenuItem, { 
      merchantId: string; 
      menuItemId: string; 
      available: boolean 
    }>({
      query: ({ merchantId, menuItemId, available }) => ({
        url: `/merchants/${merchantId}/menu-items/${menuItemId}/availability`,
        method: 'PATCH',
        body: { available },
      }),
      invalidatesTags: (result, error, { merchantId, menuItemId }) => [
        { type: 'MenuItems', id: menuItemId },
        { type: 'MenuItems', id: merchantId },
      ],
    }),

    // Get menu categories
    getMenuCategories: builder.query<MenuCategoriesResponse, { merchantId: string }>({
      query: ({ merchantId }) => ({
        url: `/merchants/${merchantId}/menu-items/categories`,
        method: 'GET',
      }),
      providesTags: (result, error, { merchantId }) => [
        { type: 'MenuItems', id: `${merchantId}-categories` },
      ],
    }),

    // Get featured menu items
    getFeaturedMenuItems: builder.query<MenuItem[], { merchantId: string; limit?: number }>({
      query: ({ merchantId, limit = 6 }) => ({
        url: `/merchants/${merchantId}/menu-items/featured?limit=${limit}`,
        method: 'GET',
      }),
      providesTags: (result, error, { merchantId }) => [
        { type: 'MenuItems', id: `${merchantId}-featured` },
      ],
    }),

    // Bulk update menu items
    bulkUpdateMenuItems: builder.mutation<MenuItem[], {
      merchantId: string;
      updates: Array<{ id: string; updates: Partial<MenuItem> }>;
    }>({
      query: ({ merchantId, updates }) => ({
        url: `/merchants/${merchantId}/menu-items/bulk-update`,
        method: 'PATCH',
        body: { updates },
      }),
      invalidatesTags: (result, error, { merchantId }) => [
        { type: 'MenuItems', id: merchantId },
      ],
    }),

    // Reorder menu items
    reorderMenuItems: builder.mutation<void, {
      merchantId: string;
      itemOrders: Array<{ id: string; sortOrder: number }>;
    }>({
      query: ({ merchantId, itemOrders }) => ({
        url: `/merchants/${merchantId}/menu-items/reorder`,
        method: 'PATCH',
        body: { itemOrders },
      }),
      invalidatesTags: (result, error, { merchantId }) => [
        { type: 'MenuItems', id: merchantId },
      ],
    }),
  }),
});

export const {
  useGetMenuItemsQuery,
  useGetMenuItemQuery,
  useGetMenuItemBySlugQuery,
  useCreateMenuItemMutation,
  useUpdateMenuItemMutation,
  useDeleteMenuItemMutation,
  useUpdateMenuItemAvailabilityMutation,
  useGetMenuCategoriesQuery,
  useGetFeaturedMenuItemsQuery,
  useBulkUpdateMenuItemsMutation,
  useReorderMenuItemsMutation,
} = menuItemApi;
