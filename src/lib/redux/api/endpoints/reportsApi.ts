/**
 * RTK Query API endpoints for reports and analytics
 * Handles all report-related API calls
 */

import { apiSlice } from '../apiSlice';

export interface SalesTrendData {
  period: string;
  sales: number;
  orders: number;
  date: string;
}

export interface PopularMenuItem {
  id: string;
  name: string;
  category: string;
  orders: number;
  revenue: number;
  image?: string;
  price: number;
}

export interface CustomerAnalytics {
  totalCustomers: number;
  newCustomers: number;
  returningCustomers: number;
  averageSpend: number;
  repeatCustomerRate: number;
  customerLifetimeValue: number;
}

export interface RevenueAnalytics {
  totalRevenue: number;
  revenueGrowth: number;
  averageOrderValue: number;
  totalOrders: number;
  orderGrowth: number;
}

export interface ReportsResponse {
  salesTrends: SalesTrendData[];
  popularItems: PopularMenuItem[];
  customerAnalytics: CustomerAnalytics;
  revenueAnalytics: RevenueAnalytics;
  period: string;
  generatedAt: string;
}

export interface ReportsFilters {
  period?: 'day' | 'week' | 'month' | 'quarter' | 'year';
  startDate?: string;
  endDate?: string;
  branchId?: string;
  category?: string;
}

export interface DashboardStats {
  todayOrders: number;
  todayRevenue: number;
  todayReservations: number;
  activeStaff: number;
  ordersGrowth: number;
  revenueGrowth: number;
  reservationsGrowth: number;
}

export const reportsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get comprehensive reports for a branch
    getBranchReports: builder.query<ReportsResponse, {
      merchantId: string;
      branchId: string;
      filters?: ReportsFilters;
    }>({
      query: ({ merchantId, branchId, filters = {} }) => {
        const params = new URLSearchParams();
        
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/reports?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result, error, { branchId }) => [
        { type: 'Reports', id: branchId },
      ],
    }),

    // Get sales trends data
    getSalesTrends: builder.query<SalesTrendData[], {
      merchantId: string;
      branchId: string;
      filters?: ReportsFilters;
    }>({
      query: ({ merchantId, branchId, filters = {} }) => {
        const params = new URLSearchParams();
        
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/reports/sales-trends?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result, error, { branchId }) => [
        { type: 'Reports', id: `${branchId}-sales` },
      ],
    }),

    // Get popular menu items
    getPopularItems: builder.query<PopularMenuItem[], {
      merchantId: string;
      branchId: string;
      filters?: ReportsFilters;
    }>({
      query: ({ merchantId, branchId, filters = {} }) => {
        const params = new URLSearchParams();
        
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/reports/popular-items?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result, error, { branchId }) => [
        { type: 'Reports', id: `${branchId}-items` },
      ],
    }),

    // Get customer analytics
    getCustomerAnalytics: builder.query<CustomerAnalytics, {
      merchantId: string;
      branchId: string;
      filters?: ReportsFilters;
    }>({
      query: ({ merchantId, branchId, filters = {} }) => {
        const params = new URLSearchParams();
        
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/reports/customer-analytics?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result, error, { branchId }) => [
        { type: 'Reports', id: `${branchId}-customers` },
      ],
    }),

    // Get revenue analytics
    getRevenueAnalytics: builder.query<RevenueAnalytics, {
      merchantId: string;
      branchId: string;
      filters?: ReportsFilters;
    }>({
      query: ({ merchantId, branchId, filters = {} }) => {
        const params = new URLSearchParams();
        
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/reports/revenue-analytics?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result, error, { branchId }) => [
        { type: 'Reports', id: `${branchId}-revenue` },
      ],
    }),

    // Get dashboard statistics
    getDashboardStats: builder.query<DashboardStats, {
      merchantId: string;
      branchId: string;
    }>({
      query: ({ merchantId, branchId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/dashboard/stats`,
        method: 'GET',
      }),
      providesTags: (result, error, { branchId }) => [
        { type: 'Reports', id: `${branchId}-dashboard` },
      ],
    }),

    // Export reports
    exportReports: builder.mutation<{ downloadUrl: string }, {
      merchantId: string;
      branchId: string;
      format: 'pdf' | 'excel' | 'csv';
      filters?: ReportsFilters;
    }>({
      query: ({ merchantId, branchId, format, filters = {} }) => {
        const params = new URLSearchParams();
        params.append('format', format);
        
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/reports/export?${params.toString()}`,
          method: 'POST',
        };
      },
    }),

    // Get real-time metrics
    getRealTimeMetrics: builder.query<{
      activeOrders: number;
      todayRevenue: number;
      onlineCustomers: number;
      averageWaitTime: number;
    }, {
      merchantId: string;
      branchId: string;
    }>({
      query: ({ merchantId, branchId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/metrics/realtime`,
        method: 'GET',
      }),
      providesTags: (result, error, { branchId }) => [
        { type: 'Reports', id: `${branchId}-realtime` },
      ],
    }),
  }),
});

export const {
  useGetBranchReportsQuery,
  useGetSalesTrendsQuery,
  useGetPopularItemsQuery,
  useGetCustomerAnalyticsQuery,
  useGetRevenueAnalyticsQuery,
  useGetDashboardStatsQuery,
  useExportReportsMutation,
  useGetRealTimeMetricsQuery,
} = reportsApi;
