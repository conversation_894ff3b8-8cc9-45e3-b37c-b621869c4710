/**
 * RTK Query API endpoints for staff management
 * Handles all staff-related API calls
 */

import { apiSlice } from '../apiSlice';

export interface StaffMember {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  slug: string;
  avatar?: string;
  position: string;
  department: string;
  roleId: string;
  roleName: string;
  permissions: string[];
  status: 'active' | 'inactive' | 'suspended';
  employeeId: string;
  hireDate: string;
  salary?: number;
  hourlyRate?: number;
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  emergencyContact?: {
    name: string;
    relationship: string;
    phone: string;
  };
  schedule?: WorkSchedule[];
  performance?: PerformanceMetrics;
  createdAt: string;
  updatedAt: string;
}

export interface WorkSchedule {
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  startTime: string;
  endTime: string;
  isWorkingDay: boolean;
}

export interface PerformanceMetrics {
  ordersServed: number;
  customerRating: number;
  punctualityScore: number;
  salesGenerated: number;
  lastReviewDate?: string;
}

export interface StaffRole {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  isActive: boolean;
  createdAt: string;
}

export interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  isActive: boolean;
}

export interface StaffFilters {
  status?: string;
  roleId?: string;
  department?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface CreateStaffRequest {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position: string;
  department: string;
  roleId: string;
  employeeId: string;
  hireDate: string;
  salary?: number;
  hourlyRate?: number;
  address?: StaffMember['address'];
  emergencyContact?: StaffMember['emergencyContact'];
  schedule?: WorkSchedule[];
}

export interface UpdateStaffRequest {
  id: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  position?: string;
  department?: string;
  roleId?: string;
  status?: string;
  salary?: number;
  hourlyRate?: number;
  address?: StaffMember['address'];
  emergencyContact?: StaffMember['emergencyContact'];
  schedule?: WorkSchedule[];
}

export interface StaffResponse {
  data: StaffMember[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export const staffApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get staff members with filters and pagination
    getStaff: builder.query<StaffResponse, {
      merchantId: string;
      branchId: string;
      filters?: StaffFilters;
    }>({
      query: ({ merchantId, branchId, filters = {} }) => {
        const params = new URLSearchParams();
        
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/staff?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result) => [
        'Staff',
        ...(result?.data || []).map(({ id }) => ({ type: 'Staff' as const, id })),
      ],
    }),

    // Get single staff member
    getStaffMember: builder.query<StaffMember, {
      merchantId: string;
      branchId: string;
      staffId: string;
    }>({
      query: ({ merchantId, branchId, staffId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/staff/${staffId}`,
        method: 'GET',
      }),
      providesTags: (result, error, { staffId }) => [
        { type: 'Staff', id: staffId },
      ],
    }),

    // Get staff member by slug
    getStaffMemberBySlug: builder.query<StaffMember, {
      merchantId: string;
      branchId: string;
      slug: string;
    }>({
      query: ({ merchantId, branchId, slug }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/staff/slug/${slug}`,
        method: 'GET',
      }),
      providesTags: (result, error, { slug }) => [
        { type: 'Staff', id: slug },
      ],
    }),

    // Get staff roles
    getStaffRoles: builder.query<StaffRole[], {
      merchantId: string;
      branchId: string;
    }>({
      query: ({ merchantId, branchId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/staff/roles`,
        method: 'GET',
      }),
      providesTags: ['StaffRoles'],
    }),

    // Get permissions
    getPermissions: builder.query<Permission[], {
      merchantId: string;
      branchId: string;
    }>({
      query: ({ merchantId, branchId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/staff/permissions`,
        method: 'GET',
      }),
      providesTags: ['Permissions'],
    }),

    // Create new staff member
    createStaffMember: builder.mutation<StaffMember, {
      merchantId: string;
      branchId: string;
      staffData: CreateStaffRequest;
    }>({
      query: ({ merchantId, branchId, staffData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/staff`,
        method: 'POST',
        body: staffData,
      }),
      invalidatesTags: ['Staff'],
    }),

    // Update staff member
    updateStaffMember: builder.mutation<StaffMember, {
      merchantId: string;
      branchId: string;
      staffData: UpdateStaffRequest;
    }>({
      query: ({ merchantId, branchId, staffData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/staff/${staffData.id}`,
        method: 'PUT',
        body: staffData,
      }),
      invalidatesTags: (result, error, { staffData }) => [
        { type: 'Staff', id: staffData.id },
        'Staff',
      ],
    }),

    // Delete staff member
    deleteStaffMember: builder.mutation<void, {
      merchantId: string;
      branchId: string;
      staffId: string;
    }>({
      query: ({ merchantId, branchId, staffId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/staff/${staffId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { staffId }) => [
        { type: 'Staff', id: staffId },
        'Staff',
      ],
    }),

    // Update staff status
    updateStaffStatus: builder.mutation<StaffMember, {
      merchantId: string;
      branchId: string;
      staffId: string;
      status: string;
    }>({
      query: ({ merchantId, branchId, staffId, status }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/staff/${staffId}/status`,
        method: 'PATCH',
        body: { status },
      }),
      invalidatesTags: (result, error, { staffId }) => [
        { type: 'Staff', id: staffId },
        'Staff',
      ],
    }),

    // Update staff schedule
    updateStaffSchedule: builder.mutation<StaffMember, {
      merchantId: string;
      branchId: string;
      staffId: string;
      schedule: WorkSchedule[];
    }>({
      query: ({ merchantId, branchId, staffId, schedule }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/staff/${staffId}/schedule`,
        method: 'PUT',
        body: { schedule },
      }),
      invalidatesTags: (result, error, { staffId }) => [
        { type: 'Staff', id: staffId },
      ],
    }),

    // Upload staff avatar
    uploadStaffAvatar: builder.mutation<{ avatarUrl: string }, {
      merchantId: string;
      branchId: string;
      staffId: string;
      file: File;
    }>({
      query: ({ merchantId, branchId, staffId, file }) => {
        const formData = new FormData();
        formData.append('avatar', file);
        
        return {
          url: `/merchants/${merchantId}/branches/${branchId}/staff/${staffId}/avatar`,
          method: 'POST',
          body: formData,
        };
      },
      invalidatesTags: (result, error, { staffId }) => [
        { type: 'Staff', id: staffId },
      ],
    }),

    // Create staff role
    createStaffRole: builder.mutation<StaffRole, {
      merchantId: string;
      branchId: string;
      roleData: {
        name: string;
        description: string;
        permissions: string[];
      };
    }>({
      query: ({ merchantId, branchId, roleData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/staff/roles`,
        method: 'POST',
        body: roleData,
      }),
      invalidatesTags: ['StaffRoles'],
    }),

    // Update staff role
    updateStaffRole: builder.mutation<StaffRole, {
      merchantId: string;
      branchId: string;
      roleId: string;
      roleData: {
        name?: string;
        description?: string;
        permissions?: string[];
        isActive?: boolean;
      };
    }>({
      query: ({ merchantId, branchId, roleId, roleData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/staff/roles/${roleId}`,
        method: 'PUT',
        body: roleData,
      }),
      invalidatesTags: ['StaffRoles', 'Staff'],
    }),

    // Delete staff role
    deleteStaffRole: builder.mutation<void, {
      merchantId: string;
      branchId: string;
      roleId: string;
    }>({
      query: ({ merchantId, branchId, roleId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/staff/roles/${roleId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['StaffRoles', 'Staff'],
    }),
  }),
});

export const {
  useGetStaffQuery,
  useGetStaffMemberQuery,
  useGetStaffMemberBySlugQuery,
  useGetStaffRolesQuery,
  useGetPermissionsQuery,
  useCreateStaffMemberMutation,
  useUpdateStaffMemberMutation,
  useDeleteStaffMemberMutation,
  useUpdateStaffStatusMutation,
  useUpdateStaffScheduleMutation,
  useUploadStaffAvatarMutation,
  useCreateStaffRoleMutation,
  useUpdateStaffRoleMutation,
  useDeleteStaffRoleMutation,
} = staffApi;
