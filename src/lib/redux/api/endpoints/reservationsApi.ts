/**
 * RTK Query API endpoints for reservations
 * Handles all reservation-related API calls
 */

import { apiSlice } from '../apiSlice';

export interface Reservation {
  id: string;
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  partySize: number;
  date: string;
  time: string;
  duration: number;
  tableId?: string;
  tableName?: string;
  status: 'pending' | 'confirmed' | 'seated' | 'completed' | 'cancelled' | 'no-show';
  specialRequests?: string;
  notes?: string;
  source: 'phone' | 'website' | 'walk-in' | 'app';
  createdAt: string;
  updatedAt: string;
}

export interface ReservationFilters {
  status?: string;
  date?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
  tableId?: string;
  page?: number;
  limit?: number;
}

export interface CreateReservationRequest {
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  partySize: number;
  date: string;
  time: string;
  duration?: number;
  tableId?: string;
  specialRequests?: string;
  notes?: string;
  source?: string;
}

export interface UpdateReservationRequest {
  id: string;
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  partySize?: number;
  date?: string;
  time?: string;
  duration?: number;
  tableId?: string;
  status?: string;
  specialRequests?: string;
  notes?: string;
}

export interface ReservationsResponse {
  data: Reservation[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export interface AvailableSlot {
  time: string;
  availableTables: number;
  suggestedTables: string[];
}

export const reservationsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get reservations with filters and pagination
    getReservations: builder.query<ReservationsResponse, {
      merchantId: string;
      branchId: string;
      filters?: ReservationFilters;
    }>({
      query: ({ merchantId, branchId, filters = {} }) => {
        const params = new URLSearchParams();
        
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/reservations?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result) => [
        'Reservations',
        ...(result?.data || []).map(({ id }) => ({ type: 'Reservations' as const, id })),
      ],
    }),

    // Get single reservation
    getReservation: builder.query<Reservation, {
      merchantId: string;
      branchId: string;
      reservationId: string;
    }>({
      query: ({ merchantId, branchId, reservationId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reservations/${reservationId}`,
        method: 'GET',
      }),
      providesTags: (result, error, { reservationId }) => [
        { type: 'Reservations', id: reservationId },
      ],
    }),

    // Get today's reservations
    getTodayReservations: builder.query<Reservation[], {
      merchantId: string;
      branchId: string;
    }>({
      query: ({ merchantId, branchId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reservations/today`,
        method: 'GET',
      }),
      providesTags: ['Reservations'],
    }),

    // Get available time slots
    getAvailableSlots: builder.query<AvailableSlot[], {
      merchantId: string;
      branchId: string;
      date: string;
      partySize: number;
    }>({
      query: ({ merchantId, branchId, date, partySize }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reservations/availability?date=${date}&partySize=${partySize}`,
        method: 'GET',
      }),
      providesTags: ['Reservations'],
    }),

    // Create new reservation
    createReservation: builder.mutation<Reservation, {
      merchantId: string;
      branchId: string;
      reservationData: CreateReservationRequest;
    }>({
      query: ({ merchantId, branchId, reservationData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reservations`,
        method: 'POST',
        body: reservationData,
      }),
      invalidatesTags: ['Reservations'],
    }),

    // Update reservation
    updateReservation: builder.mutation<Reservation, {
      merchantId: string;
      branchId: string;
      reservationData: UpdateReservationRequest;
    }>({
      query: ({ merchantId, branchId, reservationData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reservations/${reservationData.id}`,
        method: 'PUT',
        body: reservationData,
      }),
      invalidatesTags: (result, error, { reservationData }) => [
        { type: 'Reservations', id: reservationData.id },
        'Reservations',
      ],
    }),

    // Update reservation status
    updateReservationStatus: builder.mutation<Reservation, {
      merchantId: string;
      branchId: string;
      reservationId: string;
      status: string;
    }>({
      query: ({ merchantId, branchId, reservationId, status }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reservations/${reservationId}/status`,
        method: 'PATCH',
        body: { status },
      }),
      invalidatesTags: (result, error, { reservationId }) => [
        { type: 'Reservations', id: reservationId },
        'Reservations',
      ],
    }),

    // Cancel reservation
    cancelReservation: builder.mutation<void, {
      merchantId: string;
      branchId: string;
      reservationId: string;
      reason?: string;
    }>({
      query: ({ merchantId, branchId, reservationId, reason }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reservations/${reservationId}/cancel`,
        method: 'POST',
        body: { reason },
      }),
      invalidatesTags: (result, error, { reservationId }) => [
        { type: 'Reservations', id: reservationId },
        'Reservations',
      ],
    }),

    // Check in reservation
    checkInReservation: builder.mutation<Reservation, {
      merchantId: string;
      branchId: string;
      reservationId: string;
      tableId?: string;
    }>({
      query: ({ merchantId, branchId, reservationId, tableId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reservations/${reservationId}/checkin`,
        method: 'POST',
        body: { tableId },
      }),
      invalidatesTags: (result, error, { reservationId }) => [
        { type: 'Reservations', id: reservationId },
        'Reservations',
      ],
    }),

    // Mark as no-show
    markNoShow: builder.mutation<Reservation, {
      merchantId: string;
      branchId: string;
      reservationId: string;
    }>({
      query: ({ merchantId, branchId, reservationId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reservations/${reservationId}/no-show`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { reservationId }) => [
        { type: 'Reservations', id: reservationId },
        'Reservations',
      ],
    }),

    // Send confirmation
    sendConfirmation: builder.mutation<{ success: boolean }, {
      merchantId: string;
      branchId: string;
      reservationId: string;
      method: 'email' | 'sms';
    }>({
      query: ({ merchantId, branchId, reservationId, method }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reservations/${reservationId}/confirm`,
        method: 'POST',
        body: { method },
      }),
    }),

    // Get reservation statistics
    getReservationStats: builder.query<{
      totalReservations: number;
      todayReservations: number;
      upcomingReservations: number;
      completedReservations: number;
      cancelledReservations: number;
      noShowRate: number;
    }, {
      merchantId: string;
      branchId: string;
      period?: string;
    }>({
      query: ({ merchantId, branchId, period = '30d' }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/reservations/stats?period=${period}`,
        method: 'GET',
      }),
      providesTags: ['Reservations'],
    }),
  }),
});

export const {
  useGetReservationsQuery,
  useGetReservationQuery,
  useGetTodayReservationsQuery,
  useGetAvailableSlotsQuery,
  useCreateReservationMutation,
  useUpdateReservationMutation,
  useUpdateReservationStatusMutation,
  useCancelReservationMutation,
  useCheckInReservationMutation,
  useMarkNoShowMutation,
  useSendConfirmationMutation,
  useGetReservationStatsQuery,
} = reservationsApi;
