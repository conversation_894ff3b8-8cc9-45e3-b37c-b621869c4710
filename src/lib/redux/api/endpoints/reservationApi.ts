/**
 * RTK Query API endpoints for reservations
 * Handles all reservation-related API calls
 */

import { apiSlice } from '../apiSlice';

export interface Reservation {
  id: string;
  merchantId: string;
  customerId?: string;
  customerName: string;
  customerEmail?: string;
  customerPhone?: string;
  date: string;
  time: string;
  partySize: number;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  tableId?: string;
  tableNumber?: string;
  specialRequests?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ReservationFilters {
  status?: string;
  date?: string;
  tableId?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface CreateReservationRequest {
  merchantId: string;
  customerName: string;
  customerEmail?: string;
  customerPhone?: string;
  date: string;
  time: string;
  partySize: number;
  tableId?: string;
  specialRequests?: string;
  notes?: string;
}

export interface UpdateReservationRequest {
  merchantId: string;
  reservationId: string;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  date?: string;
  time?: string;
  partySize?: number;
  status?: string;
  tableId?: string;
  specialRequests?: string;
  notes?: string;
}

export interface ReservationsResponse {
  data: Reservation[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export const reservationApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get reservations with filters and pagination
    getReservations: builder.query<ReservationsResponse, ReservationFilters & { merchantId: string }>({
      query: ({ merchantId, ...filters }) => {
        const params = new URLSearchParams();
        
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/reservations?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result, error, { merchantId }) => [
        { type: 'Reservations', id: merchantId },
        ...(result?.data || []).map(({ id }) => ({ type: 'Reservations' as const, id })),
      ],
    }),

    // Get single reservation
    getReservation: builder.query<Reservation, { merchantId: string; reservationId: string }>({
      query: ({ merchantId, reservationId }) => ({
        url: `/merchants/${merchantId}/reservations/${reservationId}`,
        method: 'GET',
      }),
      providesTags: (result, error, { reservationId }) => [
        { type: 'Reservations', id: reservationId },
      ],
    }),

    // Create new reservation
    createReservation: builder.mutation<Reservation, CreateReservationRequest>({
      query: ({ merchantId, ...reservationData }) => ({
        url: `/merchants/${merchantId}/reservations`,
        method: 'POST',
        body: reservationData,
      }),
      invalidatesTags: (result, error, { merchantId }) => [
        { type: 'Reservations', id: merchantId },
        'Tables', // Invalidate tables to update availability
      ],
    }),

    // Update reservation
    updateReservation: builder.mutation<Reservation, UpdateReservationRequest>({
      query: ({ merchantId, reservationId, ...updates }) => ({
        url: `/merchants/${merchantId}/reservations/${reservationId}`,
        method: 'PUT',
        body: updates,
      }),
      invalidatesTags: (result, error, { merchantId, reservationId }) => [
        { type: 'Reservations', id: reservationId },
        { type: 'Reservations', id: merchantId },
        'Tables', // Invalidate tables to update availability
      ],
    }),

    // Cancel reservation
    cancelReservation: builder.mutation<void, { merchantId: string; reservationId: string }>({
      query: ({ merchantId, reservationId }) => ({
        url: `/merchants/${merchantId}/reservations/${reservationId}/cancel`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { merchantId, reservationId }) => [
        { type: 'Reservations', id: reservationId },
        { type: 'Reservations', id: merchantId },
        'Tables', // Invalidate tables to update availability
      ],
    }),

    // Confirm reservation
    confirmReservation: builder.mutation<Reservation, { merchantId: string; reservationId: string }>({
      query: ({ merchantId, reservationId }) => ({
        url: `/merchants/${merchantId}/reservations/${reservationId}/confirm`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { merchantId, reservationId }) => [
        { type: 'Reservations', id: reservationId },
        { type: 'Reservations', id: merchantId },
        'Tables', // Invalidate tables to update availability
      ],
    }),

    // Complete reservation
    completeReservation: builder.mutation<Reservation, { merchantId: string; reservationId: string }>({
      query: ({ merchantId, reservationId }) => ({
        url: `/merchants/${merchantId}/reservations/${reservationId}/complete`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { merchantId, reservationId }) => [
        { type: 'Reservations', id: reservationId },
        { type: 'Reservations', id: merchantId },
        'Tables', // Invalidate tables to update availability
      ],
    }),

    // Get reservation statistics
    getReservationStats: builder.query<{
      totalReservations: number;
      todayReservations: number;
      upcomingReservations: number;
      statusCounts: Record<string, number>;
      monthlyStats: Array<{ month: string; count: number }>;
    }, { merchantId: string; period?: string }>({
      query: ({ merchantId, period = '30d' }) => ({
        url: `/merchants/${merchantId}/reservations/stats?period=${period}`,
        method: 'GET',
      }),
      providesTags: (result, error, { merchantId }) => [
        { type: 'Reservations', id: `${merchantId}-stats` },
      ],
    }),

    // Check table availability
    checkTableAvailability: builder.query<{
      available: boolean;
      availableTables: Array<{ id: string; number: string; capacity: number }>;
      suggestedTimes: string[];
    }, {
      merchantId: string;
      date: string;
      time: string;
      partySize: number;
      tableId?: string;
    }>({
      query: ({ merchantId, ...params }) => {
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined) {
            searchParams.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/reservations/availability?${searchParams.toString()}`,
          method: 'GET',
        };
      },
    }),
  }),
});

export const {
  useGetReservationsQuery,
  useGetReservationQuery,
  useCreateReservationMutation,
  useUpdateReservationMutation,
  useCancelReservationMutation,
  useConfirmReservationMutation,
  useCompleteReservationMutation,
  useGetReservationStatsQuery,
  useCheckTableAvailabilityQuery,
} = reservationApi;
