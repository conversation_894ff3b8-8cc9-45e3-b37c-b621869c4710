/**
 * RTK Query API endpoints for tables management
 * Handles all table-related API calls
 */

import { apiSlice } from '../apiSlice';

export interface Table {
  id: string;
  name: string;
  number: number;
  capacity: number;
  area: string;
  status: 'available' | 'occupied' | 'reserved' | 'cleaning' | 'maintenance';
  position: {
    x: number;
    y: number;
  };
  shape: 'square' | 'round' | 'rectangle';
  size: {
    width: number;
    height: number;
  };
  qrCode?: string;
  currentReservation?: {
    id: string;
    customerName: string;
    partySize: number;
    time: string;
  };
  currentOrder?: {
    id: string;
    orderNumber: string;
    total: number;
    status: string;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TableArea {
  id: string;
  name: string;
  description?: string;
  color: string;
  isActive: boolean;
  tableCount: number;
}

export interface TableLayout {
  id: string;
  name: string;
  areas: TableArea[];
  tables: Table[];
  floorPlan?: {
    width: number;
    height: number;
    backgroundImage?: string;
  };
}

export interface TableFilters {
  area?: string;
  status?: string;
  capacity?: number;
  search?: string;
}

export interface CreateTableRequest {
  name: string;
  number: number;
  capacity: number;
  area: string;
  position: {
    x: number;
    y: number;
  };
  shape: 'square' | 'round' | 'rectangle';
  size: {
    width: number;
    height: number;
  };
}

export interface UpdateTableRequest {
  id: string;
  name?: string;
  number?: number;
  capacity?: number;
  area?: string;
  position?: {
    x: number;
    y: number;
  };
  shape?: 'square' | 'round' | 'rectangle';
  size?: {
    width: number;
    height: number;
  };
  status?: string;
  isActive?: boolean;
}

export interface QRCodeData {
  tableId: string;
  qrCodeUrl: string;
  menuUrl: string;
}

export const tablesApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all tables
    getTables: builder.query<Table[], {
      merchantId: string;
      branchId: string;
      filters?: TableFilters;
    }>({
      query: ({ merchantId, branchId, filters = {} }) => {
        const params = new URLSearchParams();
        
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/merchants/${merchantId}/branches/${branchId}/tables?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result) => [
        'Tables',
        ...(result || []).map(({ id }) => ({ type: 'Tables' as const, id })),
      ],
    }),

    // Get single table
    getTable: builder.query<Table, {
      merchantId: string;
      branchId: string;
      tableId: string;
    }>({
      query: ({ merchantId, branchId, tableId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/tables/${tableId}`,
        method: 'GET',
      }),
      providesTags: (result, error, { tableId }) => [
        { type: 'Tables', id: tableId },
      ],
    }),

    // Get table layout
    getTableLayout: builder.query<TableLayout, {
      merchantId: string;
      branchId: string;
    }>({
      query: ({ merchantId, branchId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/tables/layout`,
        method: 'GET',
      }),
      providesTags: ['TableLayout'],
    }),

    // Get table areas
    getTableAreas: builder.query<TableArea[], {
      merchantId: string;
      branchId: string;
    }>({
      query: ({ merchantId, branchId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/tables/areas`,
        method: 'GET',
      }),
      providesTags: ['TableAreas'],
    }),

    // Create new table
    createTable: builder.mutation<Table, {
      merchantId: string;
      branchId: string;
      tableData: CreateTableRequest;
    }>({
      query: ({ merchantId, branchId, tableData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/tables`,
        method: 'POST',
        body: tableData,
      }),
      invalidatesTags: ['Tables', 'TableLayout'],
    }),

    // Update table
    updateTable: builder.mutation<Table, {
      merchantId: string;
      branchId: string;
      tableData: UpdateTableRequest;
    }>({
      query: ({ merchantId, branchId, tableData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/tables/${tableData.id}`,
        method: 'PUT',
        body: tableData,
      }),
      invalidatesTags: (result, error, { tableData }) => [
        { type: 'Tables', id: tableData.id },
        'Tables',
        'TableLayout',
      ],
    }),

    // Delete table
    deleteTable: builder.mutation<void, {
      merchantId: string;
      branchId: string;
      tableId: string;
    }>({
      query: ({ merchantId, branchId, tableId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/tables/${tableId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { tableId }) => [
        { type: 'Tables', id: tableId },
        'Tables',
        'TableLayout',
      ],
    }),

    // Update table status
    updateTableStatus: builder.mutation<Table, {
      merchantId: string;
      branchId: string;
      tableId: string;
      status: string;
    }>({
      query: ({ merchantId, branchId, tableId, status }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/tables/${tableId}/status`,
        method: 'PATCH',
        body: { status },
      }),
      invalidatesTags: (result, error, { tableId }) => [
        { type: 'Tables', id: tableId },
        'Tables',
        'TableLayout',
      ],
    }),

    // Generate QR code for table
    generateTableQRCode: builder.mutation<QRCodeData, {
      merchantId: string;
      branchId: string;
      tableId: string;
    }>({
      query: ({ merchantId, branchId, tableId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/tables/${tableId}/qr-code`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { tableId }) => [
        { type: 'Tables', id: tableId },
      ],
    }),

    // Get all QR codes
    getTableQRCodes: builder.query<QRCodeData[], {
      merchantId: string;
      branchId: string;
    }>({
      query: ({ merchantId, branchId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/tables/qr-codes`,
        method: 'GET',
      }),
      providesTags: ['Tables'],
    }),

    // Create table area
    createTableArea: builder.mutation<TableArea, {
      merchantId: string;
      branchId: string;
      areaData: {
        name: string;
        description?: string;
        color: string;
      };
    }>({
      query: ({ merchantId, branchId, areaData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/tables/areas`,
        method: 'POST',
        body: areaData,
      }),
      invalidatesTags: ['TableAreas', 'TableLayout'],
    }),

    // Update table area
    updateTableArea: builder.mutation<TableArea, {
      merchantId: string;
      branchId: string;
      areaId: string;
      areaData: {
        name?: string;
        description?: string;
        color?: string;
        isActive?: boolean;
      };
    }>({
      query: ({ merchantId, branchId, areaId, areaData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/tables/areas/${areaId}`,
        method: 'PUT',
        body: areaData,
      }),
      invalidatesTags: ['TableAreas', 'TableLayout'],
    }),

    // Delete table area
    deleteTableArea: builder.mutation<void, {
      merchantId: string;
      branchId: string;
      areaId: string;
    }>({
      query: ({ merchantId, branchId, areaId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/tables/areas/${areaId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['TableAreas', 'TableLayout', 'Tables'],
    }),

    // Update table layout
    updateTableLayout: builder.mutation<TableLayout, {
      merchantId: string;
      branchId: string;
      layoutData: {
        name?: string;
        floorPlan?: {
          width: number;
          height: number;
          backgroundImage?: string;
        };
        tables?: Partial<Table>[];
      };
    }>({
      query: ({ merchantId, branchId, layoutData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/tables/layout`,
        method: 'PUT',
        body: layoutData,
      }),
      invalidatesTags: ['TableLayout', 'Tables'],
    }),

    // Get table statistics
    getTableStats: builder.query<{
      totalTables: number;
      availableTables: number;
      occupiedTables: number;
      reservedTables: number;
      occupancyRate: number;
      averageTurnover: number;
    }, {
      merchantId: string;
      branchId: string;
    }>({
      query: ({ merchantId, branchId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/tables/stats`,
        method: 'GET',
      }),
      providesTags: ['Tables'],
    }),
  }),
});

export const {
  useGetTablesQuery,
  useGetTableQuery,
  useGetTableLayoutQuery,
  useGetTableAreasQuery,
  useCreateTableMutation,
  useUpdateTableMutation,
  useDeleteTableMutation,
  useUpdateTableStatusMutation,
  useGenerateTableQRCodeMutation,
  useGetTableQRCodesQuery,
  useCreateTableAreaMutation,
  useUpdateTableAreaMutation,
  useDeleteTableAreaMutation,
  useUpdateTableLayoutMutation,
  useGetTableStatsQuery,
} = tablesApi;
