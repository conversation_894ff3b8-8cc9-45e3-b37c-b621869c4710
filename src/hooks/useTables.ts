/**
 * Custom hook for tables management
 * Separates business logic from UI components
 */

import { useState, useCallback, useMemo } from 'react';
import {
  useGetTablesQuery,
  useGetTableQuery,
  useGetTableLayoutQuery,
  useGetTableAreasQuery,
  useCreateTableMutation,
  useUpdateTableMutation,
  useDeleteTableMutation,
  useUpdateTableStatusMutation,
  useGenerateTableQRCodeMutation,
  useGetTableQRCodesQuery,
  useCreateTableAreaMutation,
  useUpdateTableAreaMutation,
  useDeleteTableAreaMutation,
  useUpdateTableLayoutMutation,
  useGetTableStatsQuery,
  type Table,
  type TableArea,
  type TableFilters,
} from '@/lib/redux/api/endpoints/tablesApi';
import { errorHandlers } from '@/lib/utils/error-handling';
import { MESSAGES } from '@/lib/constants/messages';

export interface UseTablesOptions {
  merchantId: string;
  branchId: string;
  initialFilters?: TableFilters;
}

export function useTables({
  merchantId,
  branchId,
  initialFilters = {}
}: UseTablesOptions) {
  const [filters, setFilters] = useState<TableFilters>(initialFilters);
  const [selectedArea, setSelectedArea] = useState<string>('all');

  // API queries
  const {
    data: tables,
    isLoading,
    isError,
    error,
    refetch
  } = useGetTablesQuery({
    merchantId,
    branchId,
    filters
  });

  const {
    data: tableLayout,
    isLoading: isLoadingLayout,
    refetch: refetchLayout
  } = useGetTableLayoutQuery({
    merchantId,
    branchId
  });

  const {
    data: areas,
    isLoading: isLoadingAreas,
    refetch: refetchAreas
  } = useGetTableAreasQuery({
    merchantId,
    branchId
  });

  const {
    data: qrCodes,
    isLoading: isLoadingQRCodes,
    refetch: refetchQRCodes
  } = useGetTableQRCodesQuery({
    merchantId,
    branchId
  });

  const {
    data: tableStats,
    isLoading: isLoadingStats,
    refetch: refetchStats
  } = useGetTableStatsQuery({
    merchantId,
    branchId
  });

  // Mutations
  const [createTable, { isLoading: isCreating }] = useCreateTableMutation();
  const [updateTable, { isLoading: isUpdating }] = useUpdateTableMutation();
  const [deleteTable, { isLoading: isDeleting }] = useDeleteTableMutation();
  const [updateTableStatus, { isLoading: isUpdatingStatus }] = useUpdateTableStatusMutation();
  const [generateQRCode, { isLoading: isGeneratingQR }] = useGenerateTableQRCodeMutation();
  const [createArea, { isLoading: isCreatingArea }] = useCreateTableAreaMutation();
  const [updateArea, { isLoading: isUpdatingArea }] = useUpdateTableAreaMutation();
  const [deleteArea, { isLoading: isDeletingArea }] = useDeleteTableAreaMutation();
  const [updateLayout, { isLoading: isUpdatingLayout }] = useUpdateTableLayoutMutation();

  // Filter management
  const updateFilters = useCallback((newFilters: Partial<TableFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({});
    setSelectedArea('all');
  }, []);

  // Table actions
  const handleCreateTable = useCallback(async (tableData: any) => {
    try {
      await createTable({
        merchantId,
        branchId,
        tableData
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.CREATED);
      refetch();
      refetchLayout();
      refetchStats();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [createTable, merchantId, branchId, refetch, refetchLayout, refetchStats]);

  const handleUpdateTable = useCallback(async (tableId: string, updates: any) => {
    try {
      await updateTable({
        merchantId,
        branchId,
        tableData: { id: tableId, ...updates }
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.UPDATED);
      refetch();
      refetchLayout();
      refetchStats();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateTable, merchantId, branchId, refetch, refetchLayout, refetchStats]);

  const handleDeleteTable = useCallback(async (tableId: string) => {
    try {
      await deleteTable({
        merchantId,
        branchId,
        tableId
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.DELETED);
      refetch();
      refetchLayout();
      refetchStats();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [deleteTable, merchantId, branchId, refetch, refetchLayout, refetchStats]);

  const handleUpdateStatus = useCallback(async (tableId: string, status: string) => {
    try {
      await updateTableStatus({
        merchantId,
        branchId,
        tableId,
        status
      }).unwrap();
      
      errorHandlers.showSuccessToast(`Table ${status} successfully`);
      refetch();
      refetchLayout();
      refetchStats();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateTableStatus, merchantId, branchId, refetch, refetchLayout, refetchStats]);

  const handleGenerateQRCode = useCallback(async (tableId: string) => {
    try {
      const result = await generateQRCode({
        merchantId,
        branchId,
        tableId
      }).unwrap();
      
      errorHandlers.showSuccessToast('QR code generated successfully');
      refetch();
      refetchQRCodes();
      return result;
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [generateQRCode, merchantId, branchId, refetch, refetchQRCodes]);

  // Area actions
  const handleCreateArea = useCallback(async (areaData: any) => {
    try {
      await createArea({
        merchantId,
        branchId,
        areaData
      }).unwrap();
      
      errorHandlers.showSuccessToast('Area created successfully');
      refetchAreas();
      refetchLayout();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [createArea, merchantId, branchId, refetchAreas, refetchLayout]);

  const handleUpdateArea = useCallback(async (areaId: string, updates: any) => {
    try {
      await updateArea({
        merchantId,
        branchId,
        areaId,
        areaData: updates
      }).unwrap();
      
      errorHandlers.showSuccessToast('Area updated successfully');
      refetchAreas();
      refetchLayout();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateArea, merchantId, branchId, refetchAreas, refetchLayout]);

  const handleDeleteArea = useCallback(async (areaId: string) => {
    try {
      await deleteArea({
        merchantId,
        branchId,
        areaId
      }).unwrap();
      
      errorHandlers.showSuccessToast('Area deleted successfully');
      refetchAreas();
      refetchLayout();
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [deleteArea, merchantId, branchId, refetchAreas, refetchLayout, refetch]);

  const handleUpdateLayout = useCallback(async (layoutData: any) => {
    try {
      await updateLayout({
        merchantId,
        branchId,
        layoutData
      }).unwrap();
      
      errorHandlers.showSuccessToast('Layout updated successfully');
      refetchLayout();
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateLayout, merchantId, branchId, refetchLayout, refetch]);

  // Status helpers
  const getStatusColor = useCallback((status: string) => {
    switch (status.toLowerCase()) {
      case 'available':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'occupied':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'reserved':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'cleaning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'maintenance':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  }, []);

  const getStatusText = useCallback((status: string) => {
    return MESSAGES.STATUS[status.toUpperCase() as keyof typeof MESSAGES.STATUS] || status;
  }, []);

  // Data processing
  const filteredTables = useMemo(() => {
    if (!tables) return [];
    
    let filtered = tables;
    
    if (selectedArea !== 'all') {
      filtered = filtered.filter(table => table.area === selectedArea);
    }
    
    return filtered;
  }, [tables, selectedArea]);

  // Statistics
  const statusCounts = useMemo(() => {
    if (!tables) return {};
    
    return tables.reduce((acc, table) => {
      const status = table.status;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }, [tables]);

  const areaCounts = useMemo(() => {
    if (!tables) return {};
    
    return tables.reduce((acc, table) => {
      const area = table.area;
      acc[area] = (acc[area] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }, [tables]);

  const capacityStats = useMemo(() => {
    if (!tables) return { total: 0, average: 0 };
    
    const totalCapacity = tables.reduce((sum, table) => sum + table.capacity, 0);
    return {
      total: totalCapacity,
      average: tables.length > 0 ? totalCapacity / tables.length : 0
    };
  }, [tables]);

  return {
    // Data
    tables: filteredTables,
    allTables: tables || [],
    tableLayout,
    areas: areas || [],
    qrCodes: qrCodes || [],
    tableStats,
    statusCounts,
    areaCounts,
    capacityStats,
    
    // Filters
    filters,
    selectedArea,
    setSelectedArea,
    updateFilters,
    clearFilters,
    
    // Loading states
    isLoading,
    isLoadingLayout,
    isLoadingAreas,
    isLoadingQRCodes,
    isLoadingStats,
    isCreating,
    isUpdating,
    isDeleting,
    isUpdatingStatus,
    isGeneratingQR,
    isCreatingArea,
    isUpdatingArea,
    isDeletingArea,
    isUpdatingLayout,
    isError,
    error,
    
    // Table actions
    createTable: handleCreateTable,
    updateTable: handleUpdateTable,
    deleteTable: handleDeleteTable,
    updateStatus: handleUpdateStatus,
    generateQRCode: handleGenerateQRCode,
    
    // Area actions
    createArea: handleCreateArea,
    updateArea: handleUpdateArea,
    deleteArea: handleDeleteArea,
    
    // Layout actions
    updateLayout: handleUpdateLayout,
    
    // Helpers
    getStatusColor,
    getStatusText,
    refetch,
    refetchLayout,
    refetchAreas,
    refetchQRCodes,
    refetchStats,
  };
}

// Hook for single table
export function useTable(merchantId: string, branchId: string, tableId: string) {
  const {
    data: table,
    isLoading,
    isError,
    error,
    refetch
  } = useGetTableQuery({
    merchantId,
    branchId,
    tableId
  });

  const [updateTable, { isLoading: isUpdating }] = useUpdateTableMutation();
  const [updateTableStatus, { isLoading: isUpdatingStatus }] = useUpdateTableStatusMutation();
  const [generateQRCode, { isLoading: isGeneratingQR }] = useGenerateTableQRCodeMutation();

  const handleUpdate = useCallback(async (updates: any) => {
    try {
      await updateTable({
        merchantId,
        branchId,
        tableData: { id: tableId, ...updates }
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.UPDATED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateTable, merchantId, branchId, tableId, refetch]);

  const handleUpdateStatus = useCallback(async (status: string) => {
    try {
      await updateTableStatus({
        merchantId,
        branchId,
        tableId,
        status
      }).unwrap();
      
      errorHandlers.showSuccessToast(`Table ${status} successfully`);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateTableStatus, merchantId, branchId, tableId, refetch]);

  const handleGenerateQRCode = useCallback(async () => {
    try {
      const result = await generateQRCode({
        merchantId,
        branchId,
        tableId
      }).unwrap();
      
      errorHandlers.showSuccessToast('QR code generated successfully');
      refetch();
      return result;
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [generateQRCode, merchantId, branchId, tableId, refetch]);

  return {
    table,
    isLoading,
    isUpdating,
    isUpdatingStatus,
    isGeneratingQR,
    isError,
    error,
    updateTable: handleUpdate,
    updateStatus: handleUpdateStatus,
    generateQRCode: handleGenerateQRCode,
    refetch,
  };
}
