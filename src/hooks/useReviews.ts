/**
 * Custom hook for reviews management
 * Separates business logic from UI components
 */

import { useState, useCallback, useMemo } from 'react';
import {
  useGetReviewsQuery,
  useGetReviewQuery,
  useGetReviewStatsQuery,
  useGetRecentReviewsQuery,
  useGetPendingReviewsQuery,
  useRespondToReviewMutation,
  useUpdateReviewResponseMutation,
  useDeleteReviewResponseMutation,
  useUpdateReviewStatusMutation,
  useFlagReviewMutation,
  useHideReviewMutation,
  useShowReviewMutation,
  useSyncExternalReviewsMutation,
  useExportReviewsMutation,
  useGetReviewInsightsQuery,
  type Review,
  type ReviewFilters,
} from '@/lib/redux/api/endpoints/reviewsApi';
import { errorHandlers } from '@/lib/utils/error-handling';
import { MESSAGES } from '@/lib/constants/messages';

export interface UseReviewsOptions {
  merchantId: string;
  branchId: string;
  initialFilters?: ReviewFilters;
}

export function useReviews({
  merchantId,
  branchId,
  initialFilters = {}
}: UseReviewsOptions) {
  const [filters, setFilters] = useState<ReviewFilters>({
    page: 1,
    limit: 20,
    ...initialFilters
  });

  // API queries
  const {
    data: reviewsData,
    isLoading,
    isError,
    error,
    refetch
  } = useGetReviewsQuery({
    merchantId,
    branchId,
    filters
  });

  const {
    data: reviewStats,
    isLoading: isLoadingStats,
    refetch: refetchStats
  } = useGetReviewStatsQuery({
    merchantId,
    branchId
  });

  const {
    data: recentReviews,
    isLoading: isLoadingRecent,
    refetch: refetchRecent
  } = useGetRecentReviewsQuery({
    merchantId,
    branchId,
    limit: 10
  });

  const {
    data: pendingReviews,
    isLoading: isLoadingPending,
    refetch: refetchPending
  } = useGetPendingReviewsQuery({
    merchantId,
    branchId
  });

  const {
    data: reviewInsights,
    isLoading: isLoadingInsights,
    refetch: refetchInsights
  } = useGetReviewInsightsQuery({
    merchantId,
    branchId
  });

  // Mutations
  const [respondToReview, { isLoading: isResponding }] = useRespondToReviewMutation();
  const [updateResponse, { isLoading: isUpdatingResponse }] = useUpdateReviewResponseMutation();
  const [deleteResponse, { isLoading: isDeletingResponse }] = useDeleteReviewResponseMutation();
  const [updateStatus, { isLoading: isUpdatingStatus }] = useUpdateReviewStatusMutation();
  const [flagReview, { isLoading: isFlagging }] = useFlagReviewMutation();
  const [hideReview, { isLoading: isHiding }] = useHideReviewMutation();
  const [showReview, { isLoading: isShowing }] = useShowReviewMutation();
  const [syncReviews, { isLoading: isSyncing }] = useSyncExternalReviewsMutation();
  const [exportReviews, { isLoading: isExporting }] = useExportReviewsMutation();

  // Filter management
  const updateFilters = useCallback((newFilters: Partial<ReviewFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({ page: 1, limit: 20 });
  }, []);

  // Review actions
  const handleRespondToReview = useCallback(async (reviewId: string, message: string) => {
    try {
      await respondToReview({
        merchantId,
        branchId,
        reviewId,
        message
      }).unwrap();
      
      errorHandlers.showSuccessToast('Response posted successfully');
      refetch();
      refetchStats();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [respondToReview, merchantId, branchId, refetch, refetchStats]);

  const handleUpdateResponse = useCallback(async (reviewId: string, message: string) => {
    try {
      await updateResponse({
        merchantId,
        branchId,
        reviewId,
        message
      }).unwrap();
      
      errorHandlers.showSuccessToast('Response updated successfully');
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateResponse, merchantId, branchId, refetch]);

  const handleDeleteResponse = useCallback(async (reviewId: string) => {
    try {
      await deleteResponse({
        merchantId,
        branchId,
        reviewId
      }).unwrap();
      
      errorHandlers.showSuccessToast('Response deleted successfully');
      refetch();
      refetchStats();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [deleteResponse, merchantId, branchId, refetch, refetchStats]);

  const handleUpdateStatus = useCallback(async (reviewId: string, status: string) => {
    try {
      await updateStatus({
        merchantId,
        branchId,
        reviewId,
        status
      }).unwrap();
      
      errorHandlers.showSuccessToast(`Review ${status} successfully`);
      refetch();
      refetchStats();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateStatus, merchantId, branchId, refetch, refetchStats]);

  const handleFlagReview = useCallback(async (reviewId: string, reason: string) => {
    try {
      await flagReview({
        merchantId,
        branchId,
        reviewId,
        reason
      }).unwrap();
      
      errorHandlers.showSuccessToast('Review flagged successfully');
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [flagReview, merchantId, branchId, refetch]);

  const handleHideReview = useCallback(async (reviewId: string) => {
    try {
      await hideReview({
        merchantId,
        branchId,
        reviewId
      }).unwrap();
      
      errorHandlers.showSuccessToast('Review hidden successfully');
      refetch();
      refetchStats();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [hideReview, merchantId, branchId, refetch, refetchStats]);

  const handleShowReview = useCallback(async (reviewId: string) => {
    try {
      await showReview({
        merchantId,
        branchId,
        reviewId
      }).unwrap();
      
      errorHandlers.showSuccessToast('Review shown successfully');
      refetch();
      refetchStats();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [showReview, merchantId, branchId, refetch, refetchStats]);

  const handleSyncReviews = useCallback(async (sources?: string[]) => {
    try {
      const result = await syncReviews({
        merchantId,
        branchId,
        sources
      }).unwrap();
      
      errorHandlers.showSuccessToast(`Synced ${result.synced} reviews successfully`);
      refetch();
      refetchStats();
      refetchRecent();
      refetchPending();
      
      if (result.errors.length > 0) {
        console.warn('Sync errors:', result.errors);
      }
      
      return result;
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [syncReviews, merchantId, branchId, refetch, refetchStats, refetchRecent, refetchPending]);

  const handleExportReviews = useCallback(async (format: 'csv' | 'excel' | 'pdf') => {
    try {
      const result = await exportReviews({
        merchantId,
        branchId,
        format,
        filters
      }).unwrap();
      
      errorHandlers.showSuccessToast('Export started successfully');
      return result.downloadUrl;
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [exportReviews, merchantId, branchId, filters]);

  // Rating helpers
  const getRatingColor = useCallback((rating: number) => {
    if (rating >= 4) return 'text-green-600';
    if (rating >= 3) return 'text-yellow-600';
    return 'text-red-600';
  }, []);

  const getSentimentColor = useCallback((sentiment: string) => {
    switch (sentiment.toLowerCase()) {
      case 'positive':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'neutral':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'negative':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  }, []);

  const getSourceColor = useCallback((source: string) => {
    switch (source.toLowerCase()) {
      case 'google':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'yelp':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'facebook':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'tripadvisor':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'internal':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  }, []);

  // Format date
  const formatDate = useCallback((date: string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }, []);

  // Data processing
  const reviews = reviewsData?.data || [];
  const pagination = reviewsData?.pagination;

  // Statistics
  const ratingCounts = useMemo(() => {
    return reviews.reduce((acc, review) => {
      const rating = review.rating;
      acc[rating] = (acc[rating] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);
  }, [reviews]);

  const sourceCounts = useMemo(() => {
    return reviews.reduce((acc, review) => {
      const source = review.source;
      acc[source] = (acc[source] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }, [reviews]);

  const sentimentCounts = useMemo(() => {
    return reviews.reduce((acc, review) => {
      const sentiment = review.sentiment;
      acc[sentiment] = (acc[sentiment] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }, [reviews]);

  return {
    // Data
    reviews,
    reviewStats,
    recentReviews: recentReviews || [],
    pendingReviews: pendingReviews || [],
    reviewInsights,
    pagination,
    ratingCounts,
    sourceCounts,
    sentimentCounts,
    
    // Loading states
    isLoading,
    isLoadingStats,
    isLoadingRecent,
    isLoadingPending,
    isLoadingInsights,
    isResponding,
    isUpdatingResponse,
    isDeletingResponse,
    isUpdatingStatus,
    isFlagging,
    isHiding,
    isShowing,
    isSyncing,
    isExporting,
    isError,
    error,
    
    // Filters
    filters,
    updateFilters,
    clearFilters,
    
    // Actions
    respondToReview: handleRespondToReview,
    updateResponse: handleUpdateResponse,
    deleteResponse: handleDeleteResponse,
    updateStatus: handleUpdateStatus,
    flagReview: handleFlagReview,
    hideReview: handleHideReview,
    showReview: handleShowReview,
    syncReviews: handleSyncReviews,
    exportReviews: handleExportReviews,
    
    // Helpers
    getRatingColor,
    getSentimentColor,
    getSourceColor,
    formatDate,
    refetch,
    refetchStats,
    refetchRecent,
    refetchPending,
    refetchInsights,
  };
}

// Hook for single review
export function useReview(merchantId: string, branchId: string, reviewId: string) {
  const {
    data: review,
    isLoading,
    isError,
    error,
    refetch
  } = useGetReviewQuery({
    merchantId,
    branchId,
    reviewId
  });

  const [respondToReview, { isLoading: isResponding }] = useRespondToReviewMutation();
  const [updateResponse, { isLoading: isUpdatingResponse }] = useUpdateReviewResponseMutation();
  const [updateStatus, { isLoading: isUpdatingStatus }] = useUpdateReviewStatusMutation();

  const handleRespond = useCallback(async (message: string) => {
    try {
      await respondToReview({
        merchantId,
        branchId,
        reviewId,
        message
      }).unwrap();
      
      errorHandlers.showSuccessToast('Response posted successfully');
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [respondToReview, merchantId, branchId, reviewId, refetch]);

  const handleUpdateResponse = useCallback(async (message: string) => {
    try {
      await updateResponse({
        merchantId,
        branchId,
        reviewId,
        message
      }).unwrap();
      
      errorHandlers.showSuccessToast('Response updated successfully');
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateResponse, merchantId, branchId, reviewId, refetch]);

  const handleUpdateStatus = useCallback(async (status: string) => {
    try {
      await updateStatus({
        merchantId,
        branchId,
        reviewId,
        status
      }).unwrap();
      
      errorHandlers.showSuccessToast(`Review ${status} successfully`);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateStatus, merchantId, branchId, reviewId, refetch]);

  return {
    review,
    isLoading,
    isResponding,
    isUpdatingResponse,
    isUpdatingStatus,
    isError,
    error,
    respondToReview: handleRespond,
    updateResponse: handleUpdateResponse,
    updateStatus: handleUpdateStatus,
    refetch,
  };
}
