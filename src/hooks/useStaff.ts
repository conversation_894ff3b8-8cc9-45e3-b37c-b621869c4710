/**
 * Custom hook for staff management
 * Separates business logic from UI components
 */

import { useState, useCallback, useMemo } from 'react';
import {
  useGetStaffQuery,
  useGetStaffMemberQuery,
  useGetStaffMemberBySlugQuery,
  useGetStaffRolesQuery,
  useGetPermissionsQuery,
  useCreateStaffMemberMutation,
  useUpdateStaffMemberMutation,
  useDeleteStaffMemberMutation,
  useUpdateStaffStatusMutation,
  useUpdateStaffScheduleMutation,
  useUploadStaffAvatarMutation,
  useCreateStaffRoleMutation,
  useUpdateStaffRoleMutation,
  useDeleteStaffRoleMutation,
  type StaffMember,
  type StaffRole,
  type Permission,
  type StaffFilters,
} from '@/lib/redux/api/endpoints/staffApi';
import { errorHandlers } from '@/lib/utils/error-handling';
import { MESSAGES } from '@/lib/constants/messages';

export interface UseStaffOptions {
  merchantId: string;
  branchId: string;
  initialFilters?: StaffFilters;
}

export function useStaff({
  merchantId,
  branchId,
  initialFilters = {}
}: UseStaffOptions) {
  const [filters, setFilters] = useState<StaffFilters>({
    page: 1,
    limit: 20,
    ...initialFilters
  });

  // API queries
  const {
    data: staffData,
    isLoading,
    isError,
    error,
    refetch
  } = useGetStaffQuery({
    merchantId,
    branchId,
    filters
  });

  const {
    data: roles,
    isLoading: isLoadingRoles,
    refetch: refetchRoles
  } = useGetStaffRolesQuery({
    merchantId,
    branchId
  });

  const {
    data: permissions,
    isLoading: isLoadingPermissions,
    refetch: refetchPermissions
  } = useGetPermissionsQuery({
    merchantId,
    branchId
  });

  // Mutations
  const [createStaffMember, { isLoading: isCreating }] = useCreateStaffMemberMutation();
  const [updateStaffMember, { isLoading: isUpdating }] = useUpdateStaffMemberMutation();
  const [deleteStaffMember, { isLoading: isDeleting }] = useDeleteStaffMemberMutation();
  const [updateStaffStatus, { isLoading: isUpdatingStatus }] = useUpdateStaffStatusMutation();
  const [updateStaffSchedule, { isLoading: isUpdatingSchedule }] = useUpdateStaffScheduleMutation();
  const [uploadAvatar, { isLoading: isUploadingAvatar }] = useUploadStaffAvatarMutation();
  const [createRole, { isLoading: isCreatingRole }] = useCreateStaffRoleMutation();
  const [updateRole, { isLoading: isUpdatingRole }] = useUpdateStaffRoleMutation();
  const [deleteRole, { isLoading: isDeletingRole }] = useDeleteStaffRoleMutation();

  // Filter management
  const updateFilters = useCallback((newFilters: Partial<StaffFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({ page: 1, limit: 20 });
  }, []);

  // Staff member actions
  const handleCreateStaffMember = useCallback(async (staffData: any) => {
    try {
      await createStaffMember({
        merchantId,
        branchId,
        staffData
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.CREATED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [createStaffMember, merchantId, branchId, refetch]);

  const handleUpdateStaffMember = useCallback(async (staffId: string, updates: any) => {
    try {
      await updateStaffMember({
        merchantId,
        branchId,
        staffData: { id: staffId, ...updates }
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.UPDATED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateStaffMember, merchantId, branchId, refetch]);

  const handleDeleteStaffMember = useCallback(async (staffId: string) => {
    try {
      await deleteStaffMember({
        merchantId,
        branchId,
        staffId
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.DELETED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [deleteStaffMember, merchantId, branchId, refetch]);

  const handleUpdateStatus = useCallback(async (staffId: string, status: string) => {
    try {
      await updateStaffStatus({
        merchantId,
        branchId,
        staffId,
        status
      }).unwrap();
      
      errorHandlers.showSuccessToast(`Staff member ${status} successfully`);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateStaffStatus, merchantId, branchId, refetch]);

  const handleUpdateSchedule = useCallback(async (staffId: string, schedule: any) => {
    try {
      await updateStaffSchedule({
        merchantId,
        branchId,
        staffId,
        schedule
      }).unwrap();
      
      errorHandlers.showSuccessToast('Schedule updated successfully');
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateStaffSchedule, merchantId, branchId, refetch]);

  const handleUploadAvatar = useCallback(async (staffId: string, file: File) => {
    try {
      const result = await uploadAvatar({
        merchantId,
        branchId,
        staffId,
        file
      }).unwrap();
      
      errorHandlers.showSuccessToast('Avatar uploaded successfully');
      refetch();
      return result.avatarUrl;
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [uploadAvatar, merchantId, branchId, refetch]);

  // Role actions
  const handleCreateRole = useCallback(async (roleData: any) => {
    try {
      await createRole({
        merchantId,
        branchId,
        roleData
      }).unwrap();
      
      errorHandlers.showSuccessToast('Role created successfully');
      refetchRoles();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [createRole, merchantId, branchId, refetchRoles]);

  const handleUpdateRole = useCallback(async (roleId: string, updates: any) => {
    try {
      await updateRole({
        merchantId,
        branchId,
        roleId,
        roleData: updates
      }).unwrap();
      
      errorHandlers.showSuccessToast('Role updated successfully');
      refetchRoles();
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateRole, merchantId, branchId, refetchRoles, refetch]);

  const handleDeleteRole = useCallback(async (roleId: string) => {
    try {
      await deleteRole({
        merchantId,
        branchId,
        roleId
      }).unwrap();
      
      errorHandlers.showSuccessToast('Role deleted successfully');
      refetchRoles();
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [deleteRole, merchantId, branchId, refetchRoles, refetch]);

  // Status helpers
  const getStatusColor = useCallback((status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'suspended':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  }, []);

  const getStatusText = useCallback((status: string) => {
    return MESSAGES.STATUS[status.toUpperCase() as keyof typeof MESSAGES.STATUS] || status;
  }, []);

  // Format currency
  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }, []);

  // Data processing
  const staff = staffData?.data || [];
  const pagination = staffData?.pagination;

  // Statistics
  const staffStats = useMemo(() => {
    return {
      totalStaff: staff.length,
      activeStaff: staff.filter(member => member.status === 'active').length,
      inactiveStaff: staff.filter(member => member.status === 'inactive').length,
      suspendedStaff: staff.filter(member => member.status === 'suspended').length,
    };
  }, [staff]);

  // Department breakdown
  const departmentStats = useMemo(() => {
    return staff.reduce((acc, member) => {
      const dept = member.department;
      acc[dept] = (acc[dept] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }, [staff]);

  // Role breakdown
  const roleStats = useMemo(() => {
    return staff.reduce((acc, member) => {
      const role = member.roleName;
      acc[role] = (acc[role] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }, [staff]);

  return {
    // Data
    staff,
    roles: roles || [],
    permissions: permissions || [],
    pagination,
    staffStats,
    departmentStats,
    roleStats,
    
    // Loading states
    isLoading,
    isLoadingRoles,
    isLoadingPermissions,
    isCreating,
    isUpdating,
    isDeleting,
    isUpdatingStatus,
    isUpdatingSchedule,
    isUploadingAvatar,
    isCreatingRole,
    isUpdatingRole,
    isDeletingRole,
    isError,
    error,
    
    // Filters
    filters,
    updateFilters,
    clearFilters,
    
    // Staff actions
    createStaffMember: handleCreateStaffMember,
    updateStaffMember: handleUpdateStaffMember,
    deleteStaffMember: handleDeleteStaffMember,
    updateStatus: handleUpdateStatus,
    updateSchedule: handleUpdateSchedule,
    uploadAvatar: handleUploadAvatar,
    
    // Role actions
    createRole: handleCreateRole,
    updateRole: handleUpdateRole,
    deleteRole: handleDeleteRole,
    
    // Helpers
    getStatusColor,
    getStatusText,
    formatCurrency,
    refetch,
    refetchRoles,
    refetchPermissions,
  };
}

// Hook for single staff member
export function useStaffMember(merchantId: string, branchId: string, staffId?: string, slug?: string) {
  const {
    data: staffMember,
    isLoading,
    isError,
    error,
    refetch
  } = staffId 
    ? useGetStaffMemberQuery({ merchantId, branchId, staffId })
    : slug 
    ? useGetStaffMemberBySlugQuery({ merchantId, branchId, slug })
    : { data: undefined, isLoading: false, isError: false, error: undefined, refetch: () => {} };

  const [updateStaffMember, { isLoading: isUpdating }] = useUpdateStaffMemberMutation();
  const [deleteStaffMember, { isLoading: isDeleting }] = useDeleteStaffMemberMutation();
  const [updateStaffStatus, { isLoading: isUpdatingStatus }] = useUpdateStaffStatusMutation();

  const handleUpdate = useCallback(async (updates: any) => {
    if (!staffMember) return;
    
    try {
      await updateStaffMember({
        merchantId,
        branchId,
        staffData: { id: staffMember.id, ...updates }
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.UPDATED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateStaffMember, merchantId, branchId, staffMember, refetch]);

  const handleDelete = useCallback(async () => {
    if (!staffMember) return;
    
    try {
      await deleteStaffMember({
        merchantId,
        branchId,
        staffId: staffMember.id
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.DELETED);
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [deleteStaffMember, merchantId, branchId, staffMember]);

  const handleUpdateStatus = useCallback(async (status: string) => {
    if (!staffMember) return;
    
    try {
      await updateStaffStatus({
        merchantId,
        branchId,
        staffId: staffMember.id,
        status
      }).unwrap();
      
      errorHandlers.showSuccessToast(`Staff member ${status} successfully`);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateStaffStatus, merchantId, branchId, staffMember, refetch]);

  return {
    staffMember,
    isLoading,
    isUpdating,
    isDeleting,
    isUpdatingStatus,
    isError,
    error,
    updateStaffMember: handleUpdate,
    deleteStaffMember: handleDelete,
    updateStatus: handleUpdateStatus,
    refetch,
  };
}
