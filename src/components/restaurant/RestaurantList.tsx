/**
 * Reusable restaurant list component
 * Handles display of multiple restaurants with filtering and pagination
 */

import React, { useState } from 'react';
import { RestaurantCard, RestaurantCardSkeleton, type Restaurant } from './RestaurantCard';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Search, Filter, Store, Plus } from 'lucide-react';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { MESSAGES } from '@/lib/constants/messages';
import { cn } from '@/lib/utils';

export interface RestaurantFilters {
  status?: string;
  search?: string;
  city?: string;
  country?: string;
}

interface RestaurantListProps {
  restaurants: Restaurant[];
  isLoading?: boolean;
  totalCount?: number;
  currentPage?: number;
  totalPages?: number;
  filters?: RestaurantFilters;
  statusCounts?: Record<string, number>;
  cityCounts?: Record<string, number>;
  onFiltersChange?: (filters: RestaurantFilters) => void;
  onPageChange?: (page: number) => void;
  onRestaurantView?: (restaurant: Restaurant) => void;
  onRestaurantEdit?: (restaurant: Restaurant) => void;
  onRestaurantDelete?: (restaurant: Restaurant) => void;
  onRestaurantCreate?: () => void;
  showFilters?: boolean;
  showPagination?: boolean;
  showCreateButton?: boolean;
  compact?: boolean;
  className?: string;
}

const STATUS_OPTIONS = [
  { value: '', label: 'All Status' },
  { value: 'active', label: MESSAGES.STATUS.ACTIVE },
  { value: 'inactive', label: MESSAGES.STATUS.INACTIVE },
  { value: 'suspended', label: 'Suspended' },
];

export function RestaurantList({
  restaurants,
  isLoading = false,
  totalCount = 0,
  currentPage = 1,
  totalPages = 1,
  filters = {},
  statusCounts = {},
  cityCounts = {},
  onFiltersChange,
  onPageChange,
  onRestaurantView,
  onRestaurantEdit,
  onRestaurantDelete,
  onRestaurantCreate,
  showFilters = true,
  showPagination = true,
  showCreateButton = true,
  compact = false,
  className
}: RestaurantListProps) {
  const [localFilters, setLocalFilters] = useState<RestaurantFilters>(filters);

  const handleFilterChange = (key: keyof RestaurantFilters, value: string) => {
    const newFilters = { ...localFilters, [key]: value || undefined };
    setLocalFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const clearFilters = () => {
    const clearedFilters = {};
    setLocalFilters(clearedFilters);
    onFiltersChange?.(clearedFilters);
  };

  const hasActiveFilters = Object.values(localFilters).some(value => value);

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-[#181510]">
            {MESSAGES.NAVIGATION.RESTAURANTS || 'Restaurants'}
          </h1>
          <p className="text-[#8a745c]">
            Manage your restaurant empire from one central dashboard
          </p>
        </div>
        
        {showCreateButton && onRestaurantCreate && (
          <Button
            onClick={onRestaurantCreate}
            className="bg-[#8a745c] hover:bg-[#6d5a48] text-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            {MESSAGES.ACTION.ADD_NEW} Restaurant
          </Button>
        )}
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="space-y-4">
          {/* Search and Status Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
              <Input
                placeholder="Search restaurants..."
                value={localFilters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10 bg-[#f1edea] border-[#e5e1dc] text-[#181510] placeholder:text-[#8a745c]"
              />
            </div>
            
            <Select
              value={localFilters.status || ''}
              onValueChange={(value) => handleFilterChange('status', value)}
            >
              <SelectTrigger className="w-full sm:w-48 bg-[#f1edea] border-[#e5e1dc]">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                {STATUS_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={localFilters.city || ''}
              onValueChange={(value) => handleFilterChange('city', value)}
            >
              <SelectTrigger className="w-full sm:w-48 bg-[#f1edea] border-[#e5e1dc]">
                <SelectValue placeholder="Filter by city" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Cities</SelectItem>
                {Object.keys(cityCounts).map((city) => (
                  <SelectItem key={city} value={city}>
                    {city} ({cityCounts[city]})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status Counts */}
          {Object.keys(statusCounts).length > 0 && (
            <div className="flex flex-wrap gap-2">
              <Badge variant="outline" className="bg-[#f9f7f4] border-[#e5e1dc]">
                <Store className="h-3 w-3 mr-1" />
                Total: {totalCount}
              </Badge>
              {Object.entries(statusCounts).map(([status, count]) => (
                <Badge
                  key={status}
                  variant="outline"
                  className="bg-[#f9f7f4] border-[#e5e1dc] cursor-pointer hover:bg-[#f1edea]"
                  onClick={() => handleFilterChange('status', status)}
                >
                  {MESSAGES.STATUS[status.toUpperCase() as keyof typeof MESSAGES.STATUS] || status}: {count}
                </Badge>
              ))}
            </div>
          )}

          {/* Clear Filters */}
          {hasActiveFilters && (
            <div className="flex justify-between items-center">
              <p className="text-sm text-[#8a745c]">
                {restaurants.length} of {totalCount} restaurants
              </p>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-[#8a745c] hover:text-[#181510]"
              >
                {MESSAGES.ACTION.CLEAR} filters
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <RestaurantCardSkeleton key={index} compact={compact} />
          ))}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && restaurants.length === 0 && (
        <div className="text-center py-12">
          <Store className="h-12 w-12 text-[#8a745c] mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-[#181510] mb-2">
            {hasActiveFilters ? MESSAGES.INFO.NO_RESULTS : MESSAGES.INFO.NO_DATA}
          </h3>
          <p className="text-[#8a745c] mb-4">
            {hasActiveFilters 
              ? 'Try adjusting your filters to see more restaurants.'
              : 'Get started by adding your first restaurant.'
            }
          </p>
          {hasActiveFilters ? (
            <Button
              variant="outline"
              onClick={clearFilters}
              className="bg-[#f1edea] border-[#e5e1dc] text-[#181510] hover:bg-[#e2dcd4]"
            >
              {MESSAGES.ACTION.CLEAR} filters
            </Button>
          ) : (
            onRestaurantCreate && (
              <Button
                onClick={onRestaurantCreate}
                className="bg-[#8a745c] hover:bg-[#6d5a48] text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                {MESSAGES.ACTION.ADD_NEW} Restaurant
              </Button>
            )
          )}
        </div>
      )}

      {/* Restaurants Grid */}
      {!isLoading && restaurants.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {restaurants.map((restaurant) => (
            <RestaurantCard
              key={restaurant.id}
              restaurant={restaurant}
              onView={onRestaurantView}
              onEdit={onRestaurantEdit}
              onDelete={onRestaurantDelete}
              compact={compact}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      {showPagination && totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => onPageChange?.(Math.max(1, currentPage - 1))}
                  className={cn(
                    currentPage === 1 && 'pointer-events-none opacity-50'
                  )}
                />
              </PaginationItem>
              
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <PaginationItem key={page}>
                    <PaginationLink
                      onClick={() => onPageChange?.(page)}
                      isActive={currentPage === page}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                );
              })}
              
              <PaginationItem>
                <PaginationNext
                  onClick={() => onPageChange?.(Math.min(totalPages, currentPage + 1))}
                  className={cn(
                    currentPage === totalPages && 'pointer-events-none opacity-50'
                  )}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
}
