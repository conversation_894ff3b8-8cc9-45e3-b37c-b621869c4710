/**
 * Reusable restaurant form component
 * Handles creation and editing of restaurants
 */

import React from 'react';
import { Form<PERSON>rapper, InputField, TextareaField, SelectField } from '@/components/forms';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Upload, MapPin, Phone, Mail, Globe, Clock } from 'lucide-react';
import { MESSAGES } from '@/lib/constants/messages';
import { z } from 'zod';

const restaurantSchema = z.object({
  name: z.string().min(1, MESSAGES.VALIDATION.REQUIRED),
  description: z.string().optional(),
  email: z.string().email(MESSAGES.VALIDATION.INVALID_EMAIL).optional().or(z.literal('')),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  postalCode: z.string().optional(),
  timezone: z.string().min(1, MESSAGES.VALIDATION.REQUIRED),
  currency: z.string().min(1, MESSAGES.VALIDATION.REQUIRED),
  status: z.enum(['active', 'inactive', 'suspended']),
});

export interface RestaurantFormData {
  name: string;
  description?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  timezone: string;
  currency: string;
  status: 'active' | 'inactive' | 'suspended';
  logo?: string;
}

interface RestaurantFormProps {
  initialData?: Partial<RestaurantFormData>;
  onSubmit: (data: RestaurantFormData) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  isEditing?: boolean;
  className?: string;
}

const TIMEZONE_OPTIONS = [
  { value: 'UTC', label: 'UTC' },
  { value: 'America/New_York', label: 'Eastern Time (ET)' },
  { value: 'America/Chicago', label: 'Central Time (CT)' },
  { value: 'America/Denver', label: 'Mountain Time (MT)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
  { value: 'Europe/London', label: 'London (GMT)' },
  { value: 'Europe/Paris', label: 'Paris (CET)' },
  { value: 'Asia/Tokyo', label: 'Tokyo (JST)' },
  { value: 'Asia/Shanghai', label: 'Shanghai (CST)' },
  { value: 'Asia/Bangkok', label: 'Bangkok (ICT)' },
];

const CURRENCY_OPTIONS = [
  { value: 'USD', label: 'US Dollar (USD)' },
  { value: 'EUR', label: 'Euro (EUR)' },
  { value: 'GBP', label: 'British Pound (GBP)' },
  { value: 'JPY', label: 'Japanese Yen (JPY)' },
  { value: 'CNY', label: 'Chinese Yuan (CNY)' },
  { value: 'THB', label: 'Thai Baht (THB)' },
  { value: 'CAD', label: 'Canadian Dollar (CAD)' },
  { value: 'AUD', label: 'Australian Dollar (AUD)' },
];

const STATUS_OPTIONS = [
  { value: 'active', label: MESSAGES.STATUS.ACTIVE },
  { value: 'inactive', label: MESSAGES.STATUS.INACTIVE },
  { value: 'suspended', label: 'Suspended' },
];

export function RestaurantForm({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  isEditing = false,
  className
}: RestaurantFormProps) {
  const defaultValues: RestaurantFormData = {
    name: '',
    description: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    country: '',
    postalCode: '',
    timezone: 'UTC',
    currency: 'USD',
    status: 'active',
    ...initialData,
  };

  return (
    <div className={className}>
      <FormWrapper
        schema={restaurantSchema}
        defaultValues={defaultValues}
        onSubmit={onSubmit}
        className="space-y-6"
      >
        {/* Basic Information */}
        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader>
            <CardTitle className="text-[#181510] flex items-center gap-2">
              <Globe className="h-5 w-5 text-[#8a745c]" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <InputField
                name="name"
                label="Restaurant Name"
                placeholder="Enter restaurant name"
                required
                className="bg-[#f1edea] border-[#e5e1dc]"
              />
              <SelectField
                name="status"
                label="Status"
                options={STATUS_OPTIONS}
                required
                className="bg-[#f1edea] border-[#e5e1dc]"
              />
            </div>
            
            <TextareaField
              name="description"
              label="Description"
              placeholder="Describe your restaurant..."
              rows={3}
              className="bg-[#f1edea] border-[#e5e1dc]"
            />
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader>
            <CardTitle className="text-[#181510] flex items-center gap-2">
              <Phone className="h-5 w-5 text-[#8a745c]" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <InputField
                name="email"
                label="Email"
                type="email"
                placeholder="<EMAIL>"
                className="bg-[#f1edea] border-[#e5e1dc]"
              />
              <InputField
                name="phone"
                label="Phone Number"
                placeholder="+****************"
                className="bg-[#f1edea] border-[#e5e1dc]"
              />
            </div>
          </CardContent>
        </Card>

        {/* Location Information */}
        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader>
            <CardTitle className="text-[#181510] flex items-center gap-2">
              <MapPin className="h-5 w-5 text-[#8a745c]" />
              Location Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <InputField
              name="address"
              label="Address"
              placeholder="123 Main Street"
              className="bg-[#f1edea] border-[#e5e1dc]"
            />
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <InputField
                name="city"
                label="City"
                placeholder="New York"
                className="bg-[#f1edea] border-[#e5e1dc]"
              />
              <InputField
                name="state"
                label="State/Province"
                placeholder="NY"
                className="bg-[#f1edea] border-[#e5e1dc]"
              />
              <InputField
                name="country"
                label="Country"
                placeholder="United States"
                className="bg-[#f1edea] border-[#e5e1dc]"
              />
            </div>
            
            <InputField
              name="postalCode"
              label="Postal Code"
              placeholder="10001"
              className="bg-[#f1edea] border-[#e5e1dc]"
            />
          </CardContent>
        </Card>

        {/* Business Settings */}
        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader>
            <CardTitle className="text-[#181510] flex items-center gap-2">
              <Clock className="h-5 w-5 text-[#8a745c]" />
              Business Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <SelectField
                name="timezone"
                label="Timezone"
                options={TIMEZONE_OPTIONS}
                required
                className="bg-[#f1edea] border-[#e5e1dc]"
              />
              <SelectField
                name="currency"
                label="Currency"
                options={CURRENCY_OPTIONS}
                required
                className="bg-[#f1edea] border-[#e5e1dc]"
              />
            </div>
          </CardContent>
        </Card>

        {/* Logo Upload */}
        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader>
            <CardTitle className="text-[#181510] flex items-center gap-2">
              <Upload className="h-5 w-5 text-[#8a745c]" />
              Restaurant Logo
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border-2 border-dashed border-[#e5e1dc] rounded-lg p-6 text-center">
              <Upload className="h-8 w-8 text-[#8a745c] mx-auto mb-2" />
              <p className="text-[#8a745c] text-sm mb-2">
                Drag and drop your logo here, or click to browse
              </p>
              <p className="text-xs text-[#8a745c]">
                Recommended: 400x400px, PNG or JPG, max 2MB
              </p>
              <Button
                type="button"
                variant="outline"
                className="mt-3 border-[#e5e1dc] text-[#181510] hover:bg-[#f1edea]"
              >
                Choose File
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex items-center justify-between pt-6 border-t border-[#e5e1dc]">
          <div className="flex items-center gap-2">
            {isEditing && (
              <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                {MESSAGES.ACTION.EDIT} Mode
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-3">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
                className="border-[#e5e1dc] text-[#181510] hover:bg-[#f1edea]"
              >
                {MESSAGES.ACTION.CANCEL}
              </Button>
            )}
            
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-[#8a745c] hover:bg-[#6d5a48] text-white"
            >
              {isLoading ? (
                MESSAGES.INFO.SAVING
              ) : isEditing ? (
                MESSAGES.ACTION.UPDATE
              ) : (
                MESSAGES.ACTION.CREATE
              )}
            </Button>
          </div>
        </div>
      </FormWrapper>
    </div>
  );
}
