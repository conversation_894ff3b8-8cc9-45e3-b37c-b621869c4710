"use client"

import * as React from "react"
import { use<PERSON><PERSON>, FormProvider, SubmitHandler, DefaultValues } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { errorHandlers } from "@/lib/utils/error-handling"

interface FormWrapperProps<T extends Record<string, any>> {
  schema: z.ZodSchema<T>
  defaultValues?: DefaultValues<T>
  onSubmit: SubmitHandler<T>
  children: React.ReactNode
  className?: string
  submitText?: string
  cancelText?: string
  onCancel?: () => void
  isLoading?: boolean
  disabled?: boolean
  showButtons?: boolean
  buttonClassName?: string
}

export function FormWrapper<T extends Record<string, any>>({
  schema,
  defaultValues,
  onSubmit,
  children,
  className,
  submitText = "Submit",
  cancelText = "Cancel",
  onCancel,
  isLoading = false,
  disabled = false,
  showButtons = true,
  buttonClassName,
}: FormWrapperProps<T>) {
  const methods = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues,
    mode: "onSubmit",
  })

  const {
    handleSubmit,
    formState: { isSubmitting, isDirty },
    setError,
  } = methods

  const handleFormSubmit: SubmitHandler<T> = async (data) => {
    try {
      await onSubmit(data)
    } catch (error) {
      // Handle validation errors
      if (error instanceof z.ZodError) {
        error.errors.forEach((err) => {
          const path = err.path.join('.') as keyof T
          setError(path, {
            type: 'manual',
            message: err.message,
          })
        })
      } else {
        // Show general error toast
        errorHandlers.showErrorToast(error as Error)
      }
    }
  }

  const isFormDisabled = disabled || isLoading || isSubmitting

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={handleSubmit(handleFormSubmit)}
        className={cn("space-y-6", className)}
      >
        {children}
        
        {showButtons && (
          <div className={cn("flex gap-3 pt-4", buttonClassName)}>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isFormDisabled}
                className="bg-[#f1edea] text-[#181510] border-[#e5e1dc] hover:bg-[#e2dcd4]"
              >
                {cancelText}
              </Button>
            )}
            <Button
              type="submit"
              disabled={isFormDisabled}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]"
            >
              {isSubmitting || isLoading ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  Submitting...
                </>
              ) : (
                submitText
              )}
            </Button>
          </div>
        )}
      </form>
    </FormProvider>
  )
}

// Hook for accessing form context outside of FormWrapper
export function useFormContext<T = any>() {
  const context = React.useContext(FormProvider as any)
  
  if (!context) {
    throw new Error('useFormContext must be used within a FormWrapper')
  }
  
  return context as ReturnType<typeof useForm<T>>
}

// Utility component for form sections
interface FormSectionProps {
  title?: string
  description?: string
  children: React.ReactNode
  className?: string
}

export function FormSection({
  title,
  description,
  children,
  className,
}: FormSectionProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <h3 className="text-lg font-semibold text-[#181510]">{title}</h3>
          )}
          {description && (
            <p className="text-sm text-[#8a745c]">{description}</p>
          )}
        </div>
      )}
      <div className="space-y-4">{children}</div>
    </div>
  )
}

// Utility component for form grid layout
interface FormGridProps {
  children: React.ReactNode
  columns?: 1 | 2 | 3 | 4
  className?: string
}

export function FormGrid({
  children,
  columns = 2,
  className,
}: FormGridProps) {
  const gridCols = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
  }

  return (
    <div className={cn("grid gap-4", gridCols[columns], className)}>
      {children}
    </div>
  )
}
