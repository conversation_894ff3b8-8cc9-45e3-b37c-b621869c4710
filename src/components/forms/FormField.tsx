"use client"

import * as React from "react"
import { use<PERSON>ormC<PERSON><PERSON><PERSON>, Controller } from "react-hook-form"
import { cn } from "@/lib/utils"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

interface BaseFieldProps {
  name: string
  label?: string
  description?: string
  required?: boolean
  className?: string
}

interface InputFieldProps extends BaseFieldProps {
  type?: "text" | "email" | "password" | "number" | "tel" | "url"
  placeholder?: string
}

interface TextareaFieldProps extends BaseFieldProps {
  placeholder?: string
  rows?: number
}

interface SelectFieldProps extends BaseFieldProps {
  placeholder?: string
  options: Array<{ value: string; label: string }>
}

interface CheckboxFieldProps extends BaseFieldProps {
  description?: string
}

interface RadioFieldProps extends BaseFieldProps {
  options: Array<{ value: string; label: string }>
}

// Input Field Component
export function InputField({
  name,
  label,
  description,
  required,
  className,
  type = "text",
  placeholder,
}: InputFieldProps) {
  const { control, formState: { errors } } = useFormContext()
  const error = errors[name]

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label htmlFor={name} className="text-[#181510] font-medium">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Input
            {...field}
            id={name}
            type={type}
            placeholder={placeholder}
            className={cn(
              "bg-[#f4f2f0] border-[#e5e1dc] text-[#181510] placeholder:text-[#8a745c]",
              error && "border-red-500 focus:ring-red-500"
            )}
          />
        )}
      />
      {description && (
        <p className="text-sm text-[#8a745c]">{description}</p>
      )}
      {error && (
        <p className="text-sm text-red-500">{error.message as string}</p>
      )}
    </div>
  )
}

// Textarea Field Component
export function TextareaField({
  name,
  label,
  description,
  required,
  className,
  placeholder,
  rows = 3,
}: TextareaFieldProps) {
  const { control, formState: { errors } } = useFormContext()
  const error = errors[name]

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label htmlFor={name} className="text-[#181510] font-medium">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Textarea
            {...field}
            id={name}
            placeholder={placeholder}
            rows={rows}
            className={cn(
              "bg-[#f4f2f0] border-[#e5e1dc] text-[#181510] placeholder:text-[#8a745c]",
              error && "border-red-500 focus:ring-red-500"
            )}
          />
        )}
      />
      {description && (
        <p className="text-sm text-[#8a745c]">{description}</p>
      )}
      {error && (
        <p className="text-sm text-red-500">{error.message as string}</p>
      )}
    </div>
  )
}

// Select Field Component
export function SelectField({
  name,
  label,
  description,
  required,
  className,
  placeholder,
  options,
}: SelectFieldProps) {
  const { control, formState: { errors } } = useFormContext()
  const error = errors[name]

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label htmlFor={name} className="text-[#181510] font-medium">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Select onValueChange={field.onChange} value={field.value}>
            <SelectTrigger
              className={cn(
                "bg-[#f4f2f0] border-[#e5e1dc] text-[#181510]",
                error && "border-red-500 focus:ring-red-500"
              )}
            >
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              {options.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      />
      {description && (
        <p className="text-sm text-[#8a745c]">{description}</p>
      )}
      {error && (
        <p className="text-sm text-red-500">{error.message as string}</p>
      )}
    </div>
  )
}

// Checkbox Field Component
export function CheckboxField({
  name,
  label,
  description,
  className,
}: CheckboxFieldProps) {
  const { control, formState: { errors } } = useFormContext()
  const error = errors[name]

  return (
    <div className={cn("space-y-2", className)}>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={name}
              checked={field.value}
              onCheckedChange={field.onChange}
              className="border-[#e5e1dc]"
            />
            {label && (
              <Label
                htmlFor={name}
                className="text-[#181510] font-medium cursor-pointer"
              >
                {label}
              </Label>
            )}
          </div>
        )}
      />
      {description && (
        <p className="text-sm text-[#8a745c]">{description}</p>
      )}
      {error && (
        <p className="text-sm text-red-500">{error.message as string}</p>
      )}
    </div>
  )
}

// Radio Field Component
export function RadioField({
  name,
  label,
  description,
  required,
  className,
  options,
}: RadioFieldProps) {
  const { control, formState: { errors } } = useFormContext()
  const error = errors[name]

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label className="text-[#181510] font-medium">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <RadioGroup
            onValueChange={field.onChange}
            value={field.value}
            className="flex flex-col space-y-2"
          >
            {options.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem
                  value={option.value}
                  id={`${name}-${option.value}`}
                  className="border-[#e5e1dc]"
                />
                <Label
                  htmlFor={`${name}-${option.value}`}
                  className="text-[#181510] cursor-pointer"
                >
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        )}
      />
      {description && (
        <p className="text-sm text-[#8a745c]">{description}</p>
      )}
      {error && (
        <p className="text-sm text-red-500">{error.message as string}</p>
      )}
    </div>
  )
}
