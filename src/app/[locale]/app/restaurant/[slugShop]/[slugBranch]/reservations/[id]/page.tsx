'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { useRouter } from '@/i18n/navigation';
import { Calendar, Clock, ArrowLeft, Trash2, Edit } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';

// Mock table options
const mockTableOptions = [
  { value: 'table-1', label: 'Table 1 (4 seats)' },
  { value: 'table-2', label: 'Table 2 (2 seats)' },
  { value: 'table-3', label: 'Table 3 (4 seats)' },
  { value: 'table-4', label: 'Table 4 (6 seats)' },
  { value: 'table-5', label: 'Table 5 (8 seats)' },
  { value: 'table-6', label: 'Table 6 (4 seats)' },
  { value: 'table-7', label: 'Table 7 (2 seats)' },
  { value: 'table-8', label: 'Table 8 (6 seats)' },
  { value: 'table-9', label: 'Table 9 (4 seats)' },
  { value: 'table-10', label: 'Table 10 (4 seats)' },
  { value: 'table-11', label: 'Table 11 (6 seats)' },
  { value: 'table-12', label: 'Table 12 (4 seats)' },
  { value: 'table-13', label: 'Table 13 (4 seats)' }
];

// Mock reservation data
const mockReservation = {
  id: 'res1',
  customerName: 'Sophia Clark',
  email: '<EMAIL>',
  contactNumber: '(555) 123-4567',
  reservationDate: '2024-07-20',
  reservationTime: '19:00',
  partySize: 4,
  tablePreference: 'table-12',
  tableName: 'Table 12',
  status: 'Confirmed',
  notes: 'Birthday celebration, gluten-free options requested.',
  formattedDate: 'July 20, 2024',
  formattedTime: '7:00 PM'
};

interface EditReservationPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
    id: string;
  }>;
}

export default function ReservationDetailsPage({ params }: EditReservationPageProps) {
  const { slugShop, slugBranch, id } = React.use(params);
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const [reservation, setReservation] = useState(mockReservation);
  const router = useRouter();

  // Action states
  const [isCheckingIn, setIsCheckingIn] = useState(false);
  const [isMarkingNoShow, setIsMarkingNoShow] = useState(false);
  const [isSendingConfirmation, setIsSendingConfirmation] = useState(false);

  // Load reservation data
  useEffect(() => {
    const timer = setTimeout(() => {
      // Simulate loading reservation data
      setReservation(mockReservation);
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [id]);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="text-center py-12">
        <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
        <p className="text-[#81766a] text-sm">The branch you are looking for does not exist.</p>
      </div>
    );
  }

  const handleCheckIn = () => {
    setIsCheckingIn(true);
    setTimeout(() => {
      alert('Customer checked in successfully!');
      setIsCheckingIn(false);
    }, 1500);
  };

  const handleMarkNoShow = () => {
    setIsMarkingNoShow(true);
    setTimeout(() => {
      alert('Reservation marked as no-show.');
      setIsMarkingNoShow(false);
    }, 1500);
  };

  const handleSendConfirmation = () => {
    setIsSendingConfirmation(true);
    setTimeout(() => {
      alert('Confirmation sent successfully!');
      setIsSendingConfirmation(false);
    }, 1500);
  };

  const handleModifyReservation = () => {
    router.push(`/app/restaurant/${slugShop}/${slugBranch}/reservations/${id}/edit`);
  };

  const handleCancelReservation = () => {
    if (confirm('Are you sure you want to cancel this reservation?')) {
      alert('Reservation cancelled successfully!');
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/tables`);
    }
  };

  return (
    <div className="gap-1 px-6 flex flex-1 justify-center py-5">
      <div className="layout-content-container flex flex-col max-w-[920px] flex-1">
        {/* Breadcrumb */}
        <div className="flex flex-wrap gap-2 p-4">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/tables`} className="text-[#81766a] text-base font-medium leading-normal hover:text-[#161412]">
            Reservations
          </Link>
          <span className="text-[#81766a] text-base font-medium leading-normal">/</span>
          <span className="text-[#161412] text-base font-medium leading-normal">Reservation Details</span>
        </div>

        {/* Page Header */}
        <div className="flex flex-wrap justify-between gap-3 p-4">
          <div className="flex min-w-72 flex-col gap-3">
            <p className="text-[#161412] tracking-light text-[32px] font-bold leading-tight">Reservation Details</p>
            <p className="text-[#81766a] text-sm font-normal leading-normal">View and manage reservation details</p>
          </div>
        </div>

        {/* Customer Information */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Customer Information</h2>
        <div className="p-4 grid grid-cols-[20%_1fr] gap-x-6">
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Name</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{reservation.customerName}</p>
          </div>
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Email</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{reservation.email}</p>
          </div>
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Phone Number</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{reservation.contactNumber}</p>
          </div>
        </div>

        {/* Reservation Details */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Reservation Details</h2>
        <div className="p-4 grid grid-cols-[20%_1fr] gap-x-6">
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Date</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{reservation.formattedDate}</p>
          </div>
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Time</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{reservation.formattedTime}</p>
          </div>
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Party Size</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{reservation.partySize}</p>
          </div>
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e3e1dd] py-5">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Table</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{reservation.tableName}</p>
          </div>
        </div>

        {/* Special Requests */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Special Requests</h2>
        <p className="text-[#161412] text-base font-normal leading-normal pb-3 pt-1 px-4">{reservation.notes}</p>

        {/* Action Buttons */}
        <div className="flex justify-stretch">
          <div className="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-start">
            <button
              onClick={handleModifyReservation}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e5ccb2] text-[#161412] text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#d6bd9e] transition-colors"
            >
              <span className="truncate">Modify Reservation</span>
            </button>
            <button
              onClick={handleCancelReservation}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#f4f2f1] text-[#161412] text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#e3e1dd] transition-colors"
            >
              <span className="truncate">Cancel Reservation</span>
            </button>
          </div>
        </div>
      </div>

      {/* Sidebar - Reservation Actions */}
      <div className="layout-content-container flex flex-col w-[360px]">
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Reservation Actions</h2>

        <div className="flex items-center gap-4 bg-white px-4 min-h-14 justify-between">
          <p className="text-[#161412] text-base font-normal leading-normal flex-1 truncate">Check In</p>
          <div className="shrink-0">
            <button
              onClick={handleCheckIn}
              disabled={isCheckingIn}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#f4f2f1] text-[#161412] text-sm font-medium leading-normal w-fit hover:bg-[#e3e1dd] transition-colors disabled:opacity-50"
            >
              <span className="truncate">{isCheckingIn ? 'Checking...' : 'Check In'}</span>
            </button>
          </div>
        </div>

        <div className="flex items-center gap-4 bg-white px-4 min-h-14 justify-between">
          <p className="text-[#161412] text-base font-normal leading-normal flex-1 truncate">Mark as No-Show</p>
          <div className="shrink-0">
            <button
              onClick={handleMarkNoShow}
              disabled={isMarkingNoShow}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#f4f2f1] text-[#161412] text-sm font-medium leading-normal w-fit hover:bg-[#e3e1dd] transition-colors disabled:opacity-50"
            >
              <span className="truncate">{isMarkingNoShow ? 'Marking...' : 'No-Show'}</span>
            </button>
          </div>
        </div>

        <div className="flex items-center gap-4 bg-white px-4 min-h-14 justify-between">
          <p className="text-[#161412] text-base font-normal leading-normal flex-1 truncate">Send Confirmation</p>
          <div className="shrink-0">
            <button
              onClick={handleSendConfirmation}
              disabled={isSendingConfirmation}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#f4f2f1] text-[#161412] text-sm font-medium leading-normal w-fit hover:bg-[#e3e1dd] transition-colors disabled:opacity-50"
            >
              <span className="truncate">{isSendingConfirmation ? 'Sending...' : 'Send'}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
