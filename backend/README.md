# Restaurant Management Backend

This is the Golang Gin backend for the restaurant management system.

## Tech Stack

- **Framework**: Gin (Go HTTP web framework)
- **Database**: PostgreSQL with GORM ORM
- **Authentication**: JWT tokens
- **API Documentation**: Swagger/OpenAPI
- **Environment**: Docker support

## Project Structure

```
backend/
├── cmd/
│   └── server/
│       └── main.go          # Application entry point
├── internal/
│   ├── config/              # Configuration management
│   ├── handlers/            # HTTP handlers (controllers)
│   ├── middleware/          # HTTP middleware
│   ├── models/              # Database models
│   ├── repositories/        # Data access layer
│   ├── services/            # Business logic layer
│   └── utils/               # Utility functions
├── migrations/              # Database migrations
├── docs/                    # API documentation
├── docker/                  # Docker configuration
├── scripts/                 # Build and deployment scripts
├── go.mod                   # Go module dependencies
├── go.sum                   # Go module checksums
├── Dockerfile              # Docker image definition
└── docker-compose.yml      # Docker compose configuration
```

## Getting Started

### Prerequisites

- Go 1.21 or later
- PostgreSQL 14 or later
- Docker and Docker Compose (optional)

### Environment Variables

Create a `.env` file in the backend directory:

```env
# Server Configuration
PORT=8080
GIN_MODE=debug

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=restaurant_db
DB_SSL_MODE=disable

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRY=24h

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:4000
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization

# Redis Configuration (for caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# File Upload Configuration
UPLOAD_MAX_SIZE=10MB
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp
```

### Installation

1. Clone the repository and navigate to the backend directory:
```bash
cd backend
```

2. Install dependencies:
```bash
go mod download
```

3. Run database migrations:
```bash
go run cmd/migrate/main.go up
```

4. Start the development server:
```bash
go run cmd/server/main.go
```

### Using Docker

1. Build and run with Docker Compose:
```bash
docker-compose up --build
```

This will start:
- PostgreSQL database on port 5432
- Redis cache on port 6379
- Go backend server on port 8080

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/refresh` - Refresh JWT token
- `POST /api/auth/logout` - User logout

### Merchants
- `GET /api/merchants` - List merchants
- `POST /api/merchants` - Create merchant
- `GET /api/merchants/:id` - Get merchant details
- `PUT /api/merchants/:id` - Update merchant
- `DELETE /api/merchants/:id` - Delete merchant

### Menu Items
- `GET /api/merchants/:merchantId/menu-items` - List menu items
- `POST /api/merchants/:merchantId/menu-items` - Create menu item
- `GET /api/merchants/:merchantId/menu-items/:id` - Get menu item
- `PUT /api/merchants/:merchantId/menu-items/:id` - Update menu item
- `DELETE /api/merchants/:merchantId/menu-items/:id` - Delete menu item

### Reservations
- `GET /api/merchants/:merchantId/reservations` - List reservations
- `POST /api/merchants/:merchantId/reservations` - Create reservation
- `GET /api/merchants/:merchantId/reservations/:id` - Get reservation
- `PUT /api/merchants/:merchantId/reservations/:id` - Update reservation
- `DELETE /api/merchants/:merchantId/reservations/:id` - Cancel reservation

### Orders
- `GET /api/merchants/:merchantId/orders` - List orders
- `POST /api/merchants/:merchantId/orders` - Create order
- `GET /api/merchants/:merchantId/orders/:id` - Get order
- `PUT /api/merchants/:merchantId/orders/:id` - Update order status

## Development

### Code Generation

Generate Swagger documentation:
```bash
swag init -g cmd/server/main.go -o docs/
```

### Testing

Run tests:
```bash
go test ./...
```

Run tests with coverage:
```bash
go test -cover ./...
```

### Database Migrations

Create a new migration:
```bash
go run cmd/migrate/main.go create migration_name
```

Run migrations:
```bash
go run cmd/migrate/main.go up
```

Rollback migrations:
```bash
go run cmd/migrate/main.go down
```

## Deployment

### Production Build

Build the application:
```bash
go build -o bin/server cmd/server/main.go
```

### Docker Production

Build production image:
```bash
docker build -t restaurant-backend .
```

Run production container:
```bash
docker run -p 8080:8080 --env-file .env restaurant-backend
```

## Contributing

1. Follow Go coding standards
2. Write tests for new features
3. Update API documentation
4. Use conventional commit messages
5. Ensure all tests pass before submitting PR

## License

This project is licensed under the MIT License.
