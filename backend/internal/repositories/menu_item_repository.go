package repositories

import (
	"strings"

	"gorm.io/gorm"
	"restaurant-backend/internal/models"
)

type MenuItemRepository interface {
	Create(menuItem *models.MenuItem) error
	GetByID(id string) (*models.MenuItem, error)
	GetBySlug(merchantID, slug string) (*models.MenuItem, error)
	GetByMerchantID(merchantID string, filters MenuItemFilters) ([]models.MenuItem, *PaginationResult, error)
	Update(menuItem *models.MenuItem) error
	Delete(id string) error
	UpdateAvailability(id string, available bool) error
	GetCategories(merchantID string) ([]string, error)
	GetFeaturedItems(merchantID string, limit int) ([]models.MenuItem, error)
}

type MenuItemFilters struct {
	Category  string
	Available *bool
	Featured  *bool
	Search    string
	MinPrice  *float64
	MaxPrice  *float64
	Page      int
	Limit     int
}

type menuItemRepository struct {
	db *gorm.DB
}

func NewMenuItemRepository(db *gorm.DB) MenuItemRepository {
	return &menuItemRepository{db: db}
}

func (r *menuItemRepository) Create(menuItem *models.MenuItem) error {
	return r.db.Create(menuItem).Error
}

func (r *menuItemRepository) GetByID(id string) (*models.MenuItem, error) {
	var menuItem models.MenuItem
	err := r.db.Preload("Merchant").First(&menuItem, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &menuItem, nil
}

func (r *menuItemRepository) GetBySlug(merchantID, slug string) (*models.MenuItem, error) {
	var menuItem models.MenuItem
	err := r.db.Preload("Merchant").
		First(&menuItem, "merchant_id = ? AND slug = ?", merchantID, slug).Error
	if err != nil {
		return nil, err
	}
	return &menuItem, nil
}

func (r *menuItemRepository) GetByMerchantID(merchantID string, filters MenuItemFilters) ([]models.MenuItem, *PaginationResult, error) {
	var menuItems []models.MenuItem
	var total int64

	query := r.db.Model(&models.MenuItem{}).Where("merchant_id = ?", merchantID)

	// Apply filters
	if filters.Category != "" {
		query = query.Where("category = ?", filters.Category)
	}

	if filters.Available != nil {
		query = query.Where("available = ?", *filters.Available)
	}

	if filters.Featured != nil {
		query = query.Where("featured = ?", *filters.Featured)
	}

	if filters.MinPrice != nil {
		query = query.Where("price >= ?", *filters.MinPrice)
	}

	if filters.MaxPrice != nil {
		query = query.Where("price <= ?", *filters.MaxPrice)
	}

	if filters.Search != "" {
		searchTerm := "%" + strings.ToLower(filters.Search) + "%"
		query = query.Where(
			"LOWER(name) LIKE ? OR LOWER(description) LIKE ? OR LOWER(category) LIKE ?",
			searchTerm, searchTerm, searchTerm,
		)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, nil, err
	}

	// Apply pagination
	filters.Page, filters.Limit = ValidatePagination(filters.Page, filters.Limit)
	offset := (filters.Page - 1) * filters.Limit
	query = query.Offset(offset).Limit(filters.Limit)

	// Execute query with ordering
	err := query.Order("sort_order ASC, name ASC").Find(&menuItems).Error
	if err != nil {
		return nil, nil, err
	}

	// Calculate pagination
	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))
	pagination := &PaginationResult{
		CurrentPage:     filters.Page,
		TotalPages:      totalPages,
		TotalItems:      int(total),
		ItemsPerPage:    filters.Limit,
		HasNextPage:     filters.Page < totalPages,
		HasPreviousPage: filters.Page > 1,
	}

	return menuItems, pagination, nil
}

func (r *menuItemRepository) Update(menuItem *models.MenuItem) error {
	return r.db.Save(menuItem).Error
}

func (r *menuItemRepository) Delete(id string) error {
	return r.db.Delete(&models.MenuItem{}, "id = ?", id).Error
}

func (r *menuItemRepository) UpdateAvailability(id string, available bool) error {
	return r.db.Model(&models.MenuItem{}).
		Where("id = ?", id).
		Update("available", available).Error
}

func (r *menuItemRepository) GetCategories(merchantID string) ([]string, error) {
	var categories []string
	err := r.db.Model(&models.MenuItem{}).
		Where("merchant_id = ?", merchantID).
		Distinct("category").
		Where("category IS NOT NULL AND category != ''").
		Order("category").
		Pluck("category", &categories).Error
	return categories, err
}

func (r *menuItemRepository) GetFeaturedItems(merchantID string, limit int) ([]models.MenuItem, error) {
	var menuItems []models.MenuItem
	query := r.db.Where("merchant_id = ? AND featured = ? AND available = ?", merchantID, true, true).
		Order("sort_order ASC, name ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&menuItems).Error
	return menuItems, err
}
