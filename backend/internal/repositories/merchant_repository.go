package repositories

import (
	"strings"

	"gorm.io/gorm"
	"restaurant-backend/internal/models"
)

type MerchantRepository interface {
	Create(merchant *models.Merchant) error
	GetByID(id string) (*models.Merchant, error)
	GetBySlug(slug string) (*models.Merchant, error)
	GetByUserID(userID string, filters MerchantFilters) ([]models.Merchant, *PaginationResult, error)
	Update(merchant *models.Merchant) error
	Delete(id string) error
	GetAll(filters MerchantFilters) ([]models.Merchant, *PaginationResult, error)
	AddUserToMerchant(userID, merchantID, role string) error
	RemoveUserFromMerchant(userID, merchantID string) error
	GetUserRole(userID, merchantID string) (string, error)
}

type MerchantFilters struct {
	Status   string
	Search   string
	Country  string
	City     string
	Page     int
	Limit    int
}

type merchantRepository struct {
	db *gorm.DB
}

func NewMerchantRepository(db *gorm.DB) MerchantRepository {
	return &merchantRepository{db: db}
}

func (r *merchantRepository) Create(merchant *models.Merchant) error {
	return r.db.Create(merchant).Error
}

func (r *merchantRepository) GetByID(id string) (*models.Merchant, error) {
	var merchant models.Merchant
	err := r.db.Preload("Users").Preload("Branches").First(&merchant, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &merchant, nil
}

func (r *merchantRepository) GetBySlug(slug string) (*models.Merchant, error) {
	var merchant models.Merchant
	err := r.db.Preload("Users").Preload("Branches").First(&merchant, "slug = ?", slug).Error
	if err != nil {
		return nil, err
	}
	return &merchant, nil
}

func (r *merchantRepository) GetByUserID(userID string, filters MerchantFilters) ([]models.Merchant, *PaginationResult, error) {
	var merchants []models.Merchant
	var total int64

	// Join with user_merchants table to get merchants for specific user
	query := r.db.Model(&models.Merchant{}).
		Joins("JOIN user_merchants ON merchants.id = user_merchants.merchant_id").
		Where("user_merchants.user_id = ?", userID)

	// Apply filters
	if filters.Status != "" {
		query = query.Where("merchants.status = ?", filters.Status)
	}

	if filters.Country != "" {
		query = query.Where("merchants.country = ?", filters.Country)
	}

	if filters.City != "" {
		query = query.Where("merchants.city = ?", filters.City)
	}

	if filters.Search != "" {
		searchTerm := "%" + strings.ToLower(filters.Search) + "%"
		query = query.Where(
			"LOWER(merchants.name) LIKE ? OR LOWER(merchants.description) LIKE ?",
			searchTerm, searchTerm,
		)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, nil, err
	}

	// Apply pagination
	filters.Page, filters.Limit = ValidatePagination(filters.Page, filters.Limit)
	offset := (filters.Page - 1) * filters.Limit
	query = query.Offset(offset).Limit(filters.Limit)

	// Execute query
	err := query.Select("merchants.*").Preload("Branches").Find(&merchants).Error
	if err != nil {
		return nil, nil, err
	}

	// Calculate pagination
	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))
	pagination := &PaginationResult{
		CurrentPage:     filters.Page,
		TotalPages:      totalPages,
		TotalItems:      int(total),
		ItemsPerPage:    filters.Limit,
		HasNextPage:     filters.Page < totalPages,
		HasPreviousPage: filters.Page > 1,
	}

	return merchants, pagination, nil
}

func (r *merchantRepository) Update(merchant *models.Merchant) error {
	return r.db.Save(merchant).Error
}

func (r *merchantRepository) Delete(id string) error {
	return r.db.Delete(&models.Merchant{}, "id = ?", id).Error
}

func (r *merchantRepository) GetAll(filters MerchantFilters) ([]models.Merchant, *PaginationResult, error) {
	var merchants []models.Merchant
	var total int64

	query := r.db.Model(&models.Merchant{})

	// Apply filters
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}

	if filters.Country != "" {
		query = query.Where("country = ?", filters.Country)
	}

	if filters.City != "" {
		query = query.Where("city = ?", filters.City)
	}

	if filters.Search != "" {
		searchTerm := "%" + strings.ToLower(filters.Search) + "%"
		query = query.Where(
			"LOWER(name) LIKE ? OR LOWER(description) LIKE ?",
			searchTerm, searchTerm,
		)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, nil, err
	}

	// Apply pagination
	filters.Page, filters.Limit = ValidatePagination(filters.Page, filters.Limit)
	offset := (filters.Page - 1) * filters.Limit
	query = query.Offset(offset).Limit(filters.Limit)

	// Execute query
	err := query.Preload("Branches").Order("created_at DESC").Find(&merchants).Error
	if err != nil {
		return nil, nil, err
	}

	// Calculate pagination
	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))
	pagination := &PaginationResult{
		CurrentPage:     filters.Page,
		TotalPages:      totalPages,
		TotalItems:      int(total),
		ItemsPerPage:    filters.Limit,
		HasNextPage:     filters.Page < totalPages,
		HasPreviousPage: filters.Page > 1,
	}

	return merchants, pagination, nil
}

func (r *merchantRepository) AddUserToMerchant(userID, merchantID, role string) error {
	userMerchant := &models.UserMerchant{
		UserID:     userID,
		MerchantID: merchantID,
		Role:       role,
	}
	return r.db.Create(userMerchant).Error
}

func (r *merchantRepository) RemoveUserFromMerchant(userID, merchantID string) error {
	return r.db.Where("user_id = ? AND merchant_id = ?", userID, merchantID).
		Delete(&models.UserMerchant{}).Error
}

func (r *merchantRepository) GetUserRole(userID, merchantID string) (string, error) {
	var userMerchant models.UserMerchant
	err := r.db.Where("user_id = ? AND merchant_id = ?", userID, merchantID).
		First(&userMerchant).Error
	if err != nil {
		return "", err
	}
	return userMerchant.Role, nil
}
