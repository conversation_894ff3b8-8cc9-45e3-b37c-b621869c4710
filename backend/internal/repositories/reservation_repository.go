package repositories

import (
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
	"restaurant-backend/internal/models"
)

type ReservationRepository interface {
	Create(reservation *models.Reservation) error
	GetByID(id string) (*models.Reservation, error)
	GetByMerchantID(merchantID string, filters ReservationFilters) ([]models.Reservation, *PaginationResult, error)
	Update(reservation *models.Reservation) error
	Delete(id string) error
	GetStats(merchantID string, period string) (*ReservationStats, error)
	CheckAvailability(merchantID string, date time.Time, timeSlot string, partySize int, excludeReservationID string) (*AvailabilityResult, error)
}

type ReservationFilters struct {
	Status   string
	Date     string
	TableID  string
	Search   string
	Page     int
	Limit    int
	BranchID string
}

type ReservationStats struct {
	TotalReservations    int64                    `json:"totalReservations"`
	TodayReservations    int64                    `json:"todayReservations"`
	UpcomingReservations int64                    `json:"upcomingReservations"`
	StatusCounts         map[string]int64         `json:"statusCounts"`
	MonthlyStats         []MonthlyReservationStat `json:"monthlyStats"`
}

type MonthlyReservationStat struct {
	Month string `json:"month"`
	Count int64  `json:"count"`
}

type AvailabilityResult struct {
	Available        bool                `json:"available"`
	AvailableTables  []AvailableTable    `json:"availableTables"`
	SuggestedTimes   []string           `json:"suggestedTimes"`
	ConflictingTimes []string           `json:"conflictingTimes"`
}

type AvailableTable struct {
	ID       string `json:"id"`
	Number   string `json:"number"`
	Capacity int    `json:"capacity"`
	Location string `json:"location"`
}

type reservationRepository struct {
	db *gorm.DB
}

func NewReservationRepository(db *gorm.DB) ReservationRepository {
	return &reservationRepository{db: db}
}

func (r *reservationRepository) Create(reservation *models.Reservation) error {
	return r.db.Create(reservation).Error
}

func (r *reservationRepository) GetByID(id string) (*models.Reservation, error) {
	var reservation models.Reservation
	err := r.db.Preload("Merchant").Preload("Branch").Preload("Table").Preload("Customer").
		First(&reservation, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &reservation, nil
}

func (r *reservationRepository) GetByMerchantID(merchantID string, filters ReservationFilters) ([]models.Reservation, *PaginationResult, error) {
	var reservations []models.Reservation
	var total int64

	query := r.db.Model(&models.Reservation{}).Where("merchant_id = ?", merchantID)

	// Apply filters
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}

	if filters.Date != "" {
		query = query.Where("DATE(date) = ?", filters.Date)
	}

	if filters.TableID != "" {
		query = query.Where("table_id = ?", filters.TableID)
	}

	if filters.BranchID != "" {
		query = query.Where("branch_id = ?", filters.BranchID)
	}

	if filters.Search != "" {
		searchTerm := "%" + strings.ToLower(filters.Search) + "%"
		query = query.Where(
			"LOWER(customer_name) LIKE ? OR LOWER(customer_email) LIKE ? OR LOWER(customer_phone) LIKE ?",
			searchTerm, searchTerm, searchTerm,
		)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, nil, err
	}

	// Apply pagination
	if filters.Limit <= 0 {
		filters.Limit = 10
	}
	if filters.Page <= 0 {
		filters.Page = 1
	}

	offset := (filters.Page - 1) * filters.Limit
	query = query.Offset(offset).Limit(filters.Limit)

	// Execute query with preloads
	err := query.Preload("Merchant").Preload("Branch").Preload("Table").Preload("Customer").
		Order("date DESC, time DESC").Find(&reservations).Error

	if err != nil {
		return nil, nil, err
	}

	// Calculate pagination
	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))
	pagination := &PaginationResult{
		CurrentPage:     filters.Page,
		TotalPages:      totalPages,
		TotalItems:      int(total),
		ItemsPerPage:    filters.Limit,
		HasNextPage:     filters.Page < totalPages,
		HasPreviousPage: filters.Page > 1,
	}

	return reservations, pagination, nil
}

func (r *reservationRepository) Update(reservation *models.Reservation) error {
	return r.db.Save(reservation).Error
}

func (r *reservationRepository) Delete(id string) error {
	return r.db.Delete(&models.Reservation{}, "id = ?", id).Error
}

func (r *reservationRepository) GetStats(merchantID string, period string) (*ReservationStats, error) {
	stats := &ReservationStats{
		StatusCounts: make(map[string]int64),
		MonthlyStats: []MonthlyReservationStat{},
	}

	// Calculate date range based on period
	now := time.Now()
	var startDate time.Time

	switch period {
	case "7d":
		startDate = now.AddDate(0, 0, -7)
	case "30d":
		startDate = now.AddDate(0, 0, -30)
	case "90d":
		startDate = now.AddDate(0, 0, -90)
	case "1y":
		startDate = now.AddDate(-1, 0, 0)
	default:
		startDate = now.AddDate(0, 0, -30) // Default to 30 days
	}

	// Total reservations in period
	r.db.Model(&models.Reservation{}).
		Where("merchant_id = ? AND created_at >= ?", merchantID, startDate).
		Count(&stats.TotalReservations)

	// Today's reservations
	today := now.Format("2006-01-02")
	r.db.Model(&models.Reservation{}).
		Where("merchant_id = ? AND DATE(date) = ?", merchantID, today).
		Count(&stats.TodayReservations)

	// Upcoming reservations
	r.db.Model(&models.Reservation{}).
		Where("merchant_id = ? AND date >= ? AND status IN ?", merchantID, now, []string{"pending", "confirmed"}).
		Count(&stats.UpcomingReservations)

	// Status counts
	var statusResults []struct {
		Status string
		Count  int64
	}
	r.db.Model(&models.Reservation{}).
		Select("status, COUNT(*) as count").
		Where("merchant_id = ? AND created_at >= ?", merchantID, startDate).
		Group("status").
		Scan(&statusResults)

	for _, result := range statusResults {
		stats.StatusCounts[result.Status] = result.Count
	}

	// Monthly stats for the last 12 months
	var monthlyResults []struct {
		Month string
		Count int64
	}
	r.db.Model(&models.Reservation{}).
		Select("TO_CHAR(date, 'YYYY-MM') as month, COUNT(*) as count").
		Where("merchant_id = ? AND date >= ?", merchantID, now.AddDate(-1, 0, 0)).
		Group("TO_CHAR(date, 'YYYY-MM')").
		Order("month").
		Scan(&monthlyResults)

	for _, result := range monthlyResults {
		stats.MonthlyStats = append(stats.MonthlyStats, MonthlyReservationStat{
			Month: result.Month,
			Count: result.Count,
		})
	}

	return stats, nil
}

func (r *reservationRepository) CheckAvailability(merchantID string, date time.Time, timeSlot string, partySize int, excludeReservationID string) (*AvailabilityResult, error) {
	result := &AvailabilityResult{
		Available:        false,
		AvailableTables:  []AvailableTable{},
		SuggestedTimes:   []string{},
		ConflictingTimes: []string{},
	}

	// Get all tables for the merchant that can accommodate the party size
	var tables []models.Table
	err := r.db.Where("merchant_id = ? AND capacity >= ? AND status = ?", merchantID, partySize, "available").
		Find(&tables).Error
	if err != nil {
		return nil, err
	}

	if len(tables) == 0 {
		return result, nil
	}

	// Check for existing reservations on the same date and time
	dateStr := date.Format("2006-01-02")
	var conflictingReservations []models.Reservation
	
	query := r.db.Where("merchant_id = ? AND DATE(date) = ? AND time = ? AND status IN ?",
		merchantID, dateStr, timeSlot, []string{"pending", "confirmed"})
	
	if excludeReservationID != "" {
		query = query.Where("id != ?", excludeReservationID)
	}
	
	err = query.Find(&conflictingReservations).Error
	if err != nil {
		return nil, err
	}

	// Create map of occupied table IDs
	occupiedTables := make(map[string]bool)
	for _, reservation := range conflictingReservations {
		if reservation.TableID != "" {
			occupiedTables[reservation.TableID] = true
		}
	}

	// Find available tables
	for _, table := range tables {
		if !occupiedTables[table.ID] {
			result.AvailableTables = append(result.AvailableTables, AvailableTable{
				ID:       table.ID,
				Number:   table.Number,
				Capacity: table.Capacity,
				Location: table.Location,
			})
		}
	}

	result.Available = len(result.AvailableTables) > 0

	// If no tables available at requested time, suggest alternative times
	if !result.Available {
		result.SuggestedTimes = r.generateSuggestedTimes(merchantID, dateStr, timeSlot, partySize)
	}

	return result, nil
}

func (r *reservationRepository) generateSuggestedTimes(merchantID, date, requestedTime string, partySize int) []string {
	// Generate time slots around the requested time
	timeSlots := []string{
		"11:00", "11:30", "12:00", "12:30", "13:00", "13:30", "14:00", "14:30",
		"17:00", "17:30", "18:00", "18:30", "19:00", "19:30", "20:00", "20:30", "21:00",
	}

	var suggestions []string
	for _, slot := range timeSlots {
		if slot == requestedTime {
			continue
		}

		// Check if this time slot has availability
		availability, err := r.CheckAvailability(merchantID, parseDate(date), slot, partySize, "")
		if err == nil && availability.Available {
			suggestions = append(suggestions, slot)
		}

		// Limit suggestions to 5
		if len(suggestions) >= 5 {
			break
		}
	}

	return suggestions
}

func parseDate(dateStr string) time.Time {
	date, _ := time.Parse("2006-01-02", dateStr)
	return date
}
