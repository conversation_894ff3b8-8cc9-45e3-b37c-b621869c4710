package repositories

import (
	"errors"
	"strings"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
	"restaurant-backend/internal/models"
)

type UserRepository interface {
	Create(user *models.User) error
	GetByID(id string) (*models.User, error)
	GetByEmail(email string) (*models.User, error)
	Update(user *models.User) error
	Delete(id string) error
	GetByMerchantID(merchantID string, filters UserFilters) ([]models.User, *PaginationResult, error)
	VerifyPassword(user *models.User, password string) bool
	HashPassword(password string) (string, error)
	UpdateLastLogin(userID string) error
}

type UserFilters struct {
	Role     string
	Search   string
	Status   string
	Page     int
	Limit    int
}

type userRepository struct {
	db *gorm.DB
}

func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepository{db: db}
}

func (r *userRepository) Create(user *models.User) error {
	// Hash password before saving
	if user.PasswordHash != "" {
		hashedPassword, err := r.HashPassword(user.PasswordHash)
		if err != nil {
			return err
		}
		user.PasswordHash = hashedPassword
	}

	return r.db.Create(user).Error
}

func (r *userRepository) GetByID(id string) (*models.User, error) {
	var user models.User
	err := r.db.Preload("Merchants").First(&user, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *userRepository) GetByEmail(email string) (*models.User, error) {
	var user models.User
	err := r.db.Preload("Merchants").First(&user, "email = ?", email).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *userRepository) Update(user *models.User) error {
	return r.db.Save(user).Error
}

func (r *userRepository) Delete(id string) error {
	return r.db.Delete(&models.User{}, "id = ?", id).Error
}

func (r *userRepository) GetByMerchantID(merchantID string, filters UserFilters) ([]models.User, *PaginationResult, error) {
	var users []models.User
	var total int64

	// Join with user_merchants table to get users for specific merchant
	query := r.db.Model(&models.User{}).
		Joins("JOIN user_merchants ON users.id = user_merchants.user_id").
		Where("user_merchants.merchant_id = ?", merchantID)

	// Apply filters
	if filters.Role != "" {
		query = query.Where("user_merchants.role = ?", filters.Role)
	}

	if filters.Search != "" {
		searchTerm := "%" + strings.ToLower(filters.Search) + "%"
		query = query.Where(
			"LOWER(users.name) LIKE ? OR LOWER(users.email) LIKE ?",
			searchTerm, searchTerm,
		)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, nil, err
	}

	// Apply pagination
	filters.Page, filters.Limit = ValidatePagination(filters.Page, filters.Limit)
	offset := (filters.Page - 1) * filters.Limit
	query = query.Offset(offset).Limit(filters.Limit)

	// Execute query
	err := query.Select("users.*").Find(&users).Error
	if err != nil {
		return nil, nil, err
	}

	// Calculate pagination
	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))
	pagination := &PaginationResult{
		CurrentPage:     filters.Page,
		TotalPages:      totalPages,
		TotalItems:      int(total),
		ItemsPerPage:    filters.Limit,
		HasNextPage:     filters.Page < totalPages,
		HasPreviousPage: filters.Page > 1,
	}

	return users, pagination, nil
}

func (r *userRepository) VerifyPassword(user *models.User, password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password))
	return err == nil
}

func (r *userRepository) HashPassword(password string) (string, error) {
	if len(password) < 8 {
		return "", errors.New("password must be at least 8 characters long")
	}

	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}

	return string(hashedBytes), nil
}

func (r *userRepository) UpdateLastLogin(userID string) error {
	return r.db.Model(&models.User{}).
		Where("id = ?", userID).
		Update("last_login_at", "NOW()").Error
}
