package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/services"
)

type ReservationHandler struct {
	reservationService services.ReservationService
}

func NewReservationHandler(reservationService services.ReservationService) *ReservationHandler {
	return &ReservationHandler{
		reservationService: reservationService,
	}
}

// GetReservations handles GET /merchants/:merchantId/reservations
// @Summary Get reservations
// @Description Get reservations for a merchant with filtering and pagination
// @Tags reservations
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param status query string false "Filter by status"
// @Param date query string false "Filter by date (YYYY-MM-DD)"
// @Param tableId query string false "Filter by table ID"
// @Param search query string false "Search in customer name, email, phone"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} ReservationsResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Security BearerAuth
// @Router /merchants/{merchantId}/reservations [get]
func (h *ReservationHandler) GetReservations(c *gin.Context) {
	merchantID := c.Param("merchantId")
	
	// Parse query parameters
	filters := repositories.ReservationFilters{
		Status:   c.Query("status"),
		Date:     c.Query("date"),
		TableID:  c.Query("tableId"),
		Search:   c.Query("search"),
		BranchID: c.Query("branchId"),
	}

	// Parse pagination
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			filters.Page = p
		}
	}
	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil {
			filters.Limit = l
		}
	}

	reservations, pagination, err := h.reservationService.GetReservations(merchantID, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch reservations",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, ReservationsResponse{
		Data:       reservations,
		Pagination: pagination,
	})
}

// GetReservation handles GET /merchants/:merchantId/reservations/:id
// @Summary Get reservation
// @Description Get a specific reservation by ID
// @Tags reservations
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param id path string true "Reservation ID"
// @Success 200 {object} models.Reservation
// @Failure 404 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Security BearerAuth
// @Router /merchants/{merchantId}/reservations/{id} [get]
func (h *ReservationHandler) GetReservation(c *gin.Context) {
	reservationID := c.Param("id")

	reservation, err := h.reservationService.GetReservation(reservationID)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "Reservation not found",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, reservation)
}

// CreateReservation handles POST /merchants/:merchantId/reservations
// @Summary Create reservation
// @Description Create a new reservation
// @Tags reservations
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param reservation body services.CreateReservationRequest true "Reservation data"
// @Success 201 {object} models.Reservation
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Security BearerAuth
// @Router /merchants/{merchantId}/reservations [post]
func (h *ReservationHandler) CreateReservation(c *gin.Context) {
	merchantID := c.Param("merchantId")
	
	var req services.CreateReservationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
		})
		return
	}

	// Set merchant ID from URL
	req.MerchantID = merchantID

	reservation, err := h.reservationService.CreateReservation(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to create reservation",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, reservation)
}

// UpdateReservation handles PUT /merchants/:merchantId/reservations/:id
// @Summary Update reservation
// @Description Update an existing reservation
// @Tags reservations
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param id path string true "Reservation ID"
// @Param reservation body services.UpdateReservationRequest true "Updated reservation data"
// @Success 200 {object} models.Reservation
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Security BearerAuth
// @Router /merchants/{merchantId}/reservations/{id} [put]
func (h *ReservationHandler) UpdateReservation(c *gin.Context) {
	reservationID := c.Param("id")
	
	var req services.UpdateReservationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
		})
		return
	}

	reservation, err := h.reservationService.UpdateReservation(reservationID, &req)
	if err != nil {
		statusCode := http.StatusBadRequest
		if err.Error() == "reservation not found" {
			statusCode = http.StatusNotFound
		}
		
		c.JSON(statusCode, ErrorResponse{
			Error:   "Failed to update reservation",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, reservation)
}

// CancelReservation handles POST /merchants/:merchantId/reservations/:id/cancel
// @Summary Cancel reservation
// @Description Cancel a reservation
// @Tags reservations
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param id path string true "Reservation ID"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Security BearerAuth
// @Router /merchants/{merchantId}/reservations/{id}/cancel [post]
func (h *ReservationHandler) CancelReservation(c *gin.Context) {
	reservationID := c.Param("id")

	err := h.reservationService.CancelReservation(reservationID)
	if err != nil {
		statusCode := http.StatusBadRequest
		if err.Error() == "reservation not found" {
			statusCode = http.StatusNotFound
		}
		
		c.JSON(statusCode, ErrorResponse{
			Error:   "Failed to cancel reservation",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Reservation cancelled successfully",
	})
}

// ConfirmReservation handles POST /merchants/:merchantId/reservations/:id/confirm
// @Summary Confirm reservation
// @Description Confirm a pending reservation
// @Tags reservations
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param id path string true "Reservation ID"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Security BearerAuth
// @Router /merchants/{merchantId}/reservations/{id}/confirm [post]
func (h *ReservationHandler) ConfirmReservation(c *gin.Context) {
	reservationID := c.Param("id")

	err := h.reservationService.ConfirmReservation(reservationID)
	if err != nil {
		statusCode := http.StatusBadRequest
		if err.Error() == "reservation not found" {
			statusCode = http.StatusNotFound
		}
		
		c.JSON(statusCode, ErrorResponse{
			Error:   "Failed to confirm reservation",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Reservation confirmed successfully",
	})
}

// CheckAvailability handles GET /merchants/:merchantId/reservations/availability
// @Summary Check availability
// @Description Check table availability for a specific date and time
// @Tags reservations
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param date query string true "Date (YYYY-MM-DD)"
// @Param time query string true "Time (HH:MM)"
// @Param partySize query int true "Party size"
// @Param tableId query string false "Specific table ID"
// @Success 200 {object} repositories.AvailabilityResult
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Security BearerAuth
// @Router /merchants/{merchantId}/reservations/availability [get]
func (h *ReservationHandler) CheckAvailability(c *gin.Context) {
	merchantID := c.Param("merchantId")
	
	req := services.AvailabilityRequest{
		MerchantID: merchantID,
		Date:       c.Query("date"),
		Time:       c.Query("time"),
		TableID:    c.Query("tableId"),
		ExcludeReservationID: c.Query("excludeReservationId"),
	}

	// Parse party size
	if partySizeStr := c.Query("partySize"); partySizeStr != "" {
		if partySize, err := strconv.Atoi(partySizeStr); err == nil {
			req.PartySize = partySize
		}
	}

	availability, err := h.reservationService.CheckAvailability(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Failed to check availability",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, availability)
}

// GetReservationStats handles GET /merchants/:merchantId/reservations/stats
// @Summary Get reservation statistics
// @Description Get reservation statistics for a merchant
// @Tags reservations
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param period query string false "Time period (7d, 30d, 90d, 1y)" default(30d)
// @Success 200 {object} repositories.ReservationStats
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Security BearerAuth
// @Router /merchants/{merchantId}/reservations/stats [get]
func (h *ReservationHandler) GetReservationStats(c *gin.Context) {
	merchantID := c.Param("merchantId")
	period := c.DefaultQuery("period", "30d")

	stats, err := h.reservationService.GetReservationStats(merchantID, period)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get reservation statistics",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// Response types
type ReservationsResponse struct {
	Data       interface{}                    `json:"data"`
	Pagination *repositories.PaginationResult `json:"pagination"`
}
