package models

import (
	"time"

	"gorm.io/gorm"
)

// Base model with common fields
type BaseModel struct {
	ID        string         `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	CreatedAt time.Time      `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt time.Time      `json:"updatedAt" gorm:"autoUpdateTime"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// User represents a system user
type User struct {
	BaseModel
	Email         string    `json:"email" gorm:"uniqueIndex;not null"`
	PasswordHash  string    `json:"-" gorm:"not null"`
	Name          string    `json:"name" gorm:"not null"`
	Phone         string    `json:"phone"`
	Role          string    `json:"role" gorm:"default:'user'"` // user, merchant, admin
	EmailVerified bool      `json:"emailVerified" gorm:"default:false"`
	LastLoginAt   time.Time `json:"lastLoginAt"`
	
	// Relationships
	Merchants []Merchant `json:"merchants,omitempty" gorm:"many2many:user_merchants;"`
}

// Merchant represents a restaurant/business
type Merchant struct {
	BaseModel
	Name        string `json:"name" gorm:"not null"`
	Slug        string `json:"slug" gorm:"uniqueIndex;not null"`
	Description string `json:"description"`
	Email       string `json:"email"`
	Phone       string `json:"phone"`
	Address     string `json:"address"`
	City        string `json:"city"`
	State       string `json:"state"`
	Country     string `json:"country"`
	PostalCode  string `json:"postalCode"`
	Timezone    string `json:"timezone" gorm:"default:'UTC'"`
	Currency    string `json:"currency" gorm:"default:'USD'"`
	Status      string `json:"status" gorm:"default:'active'"` // active, inactive, suspended
	
	// Business settings
	OpeningHours string `json:"openingHours" gorm:"type:jsonb"`
	Settings     string `json:"settings" gorm:"type:jsonb"`
	
	// Relationships
	Users       []User        `json:"users,omitempty" gorm:"many2many:user_merchants;"`
	Branches    []Branch      `json:"branches,omitempty"`
	MenuItems   []MenuItem    `json:"menuItems,omitempty"`
	Tables      []Table       `json:"tables,omitempty"`
	Reservations []Reservation `json:"reservations,omitempty"`
	Orders      []Order       `json:"orders,omitempty"`
}

// Branch represents a merchant location
type Branch struct {
	BaseModel
	MerchantID  string `json:"merchantId" gorm:"not null"`
	Name        string `json:"name" gorm:"not null"`
	Slug        string `json:"slug" gorm:"not null"`
	Address     string `json:"address"`
	Phone       string `json:"phone"`
	Email       string `json:"email"`
	Status      string `json:"status" gorm:"default:'active'"`
	
	// Location settings
	OpeningHours string `json:"openingHours" gorm:"type:jsonb"`
	Settings     string `json:"settings" gorm:"type:jsonb"`
	
	// Relationships
	Merchant     Merchant      `json:"merchant,omitempty"`
	Tables       []Table       `json:"tables,omitempty"`
	Reservations []Reservation `json:"reservations,omitempty"`
	Orders       []Order       `json:"orders,omitempty"`
}

// MenuItem represents a menu item
type MenuItem struct {
	BaseModel
	MerchantID   string  `json:"merchantId" gorm:"not null"`
	Name         string  `json:"name" gorm:"not null"`
	Slug         string  `json:"slug" gorm:"not null"`
	Description  string  `json:"description"`
	Price        float64 `json:"price" gorm:"not null"`
	Category     string  `json:"category"`
	Image        string  `json:"image"`
	Images       string  `json:"images" gorm:"type:jsonb"` // Array of image URLs
	Available    bool    `json:"available" gorm:"default:true"`
	Featured     bool    `json:"featured" gorm:"default:false"`
	SortOrder    int     `json:"sortOrder" gorm:"default:0"`
	
	// Nutritional info
	Calories     int    `json:"calories"`
	Ingredients  string `json:"ingredients" gorm:"type:jsonb"`
	Allergens    string `json:"allergens" gorm:"type:jsonb"`
	DietaryInfo  string `json:"dietaryInfo" gorm:"type:jsonb"` // vegetarian, vegan, gluten-free, etc.
	
	// Preparation
	PrepTime     int    `json:"prepTime"` // in minutes
	CookingTime  int    `json:"cookingTime"` // in minutes
	
	// Relationships
	Merchant     Merchant    `json:"merchant,omitempty"`
	OrderItems   []OrderItem `json:"orderItems,omitempty"`
}

// Table represents a restaurant table
type Table struct {
	BaseModel
	MerchantID string `json:"merchantId" gorm:"not null"`
	BranchID   string `json:"branchId"`
	Number     string `json:"number" gorm:"not null"`
	Name       string `json:"name"`
	Capacity   int    `json:"capacity" gorm:"not null"`
	Location   string `json:"location"` // dining, outdoor, bar, etc.
	Status     string `json:"status" gorm:"default:'available'"` // available, occupied, reserved, maintenance
	QRCode     string `json:"qrCode"`
	
	// Position and layout
	PositionX  float64 `json:"positionX"`
	PositionY  float64 `json:"positionY"`
	Shape      string  `json:"shape" gorm:"default:'rectangle'"` // rectangle, circle, square
	
	// Relationships
	Merchant     Merchant      `json:"merchant,omitempty"`
	Branch       Branch        `json:"branch,omitempty"`
	Reservations []Reservation `json:"reservations,omitempty"`
	Orders       []Order       `json:"orders,omitempty"`
}

// Reservation represents a table reservation
type Reservation struct {
	BaseModel
	MerchantID       string    `json:"merchantId" gorm:"not null"`
	BranchID         string    `json:"branchId"`
	TableID          string    `json:"tableId"`
	CustomerID       string    `json:"customerId"`
	CustomerName     string    `json:"customerName" gorm:"not null"`
	CustomerEmail    string    `json:"customerEmail"`
	CustomerPhone    string    `json:"customerPhone"`
	Date             time.Time `json:"date" gorm:"not null"`
	Time             string    `json:"time" gorm:"not null"`
	PartySize        int       `json:"partySize" gorm:"not null"`
	Status           string    `json:"status" gorm:"default:'pending'"` // pending, confirmed, completed, cancelled, no_show
	SpecialRequests  string    `json:"specialRequests"`
	Notes            string    `json:"notes"`
	
	// Confirmation
	ConfirmedAt      time.Time `json:"confirmedAt"`
	ConfirmedBy      string    `json:"confirmedBy"`
	
	// Relationships
	Merchant Merchant `json:"merchant,omitempty"`
	Branch   Branch   `json:"branch,omitempty"`
	Table    Table    `json:"table,omitempty"`
	Customer User     `json:"customer,omitempty"`
}

// Order represents a customer order
type Order struct {
	BaseModel
	MerchantID    string    `json:"merchantId" gorm:"not null"`
	BranchID      string    `json:"branchId"`
	TableID       string    `json:"tableId"`
	CustomerID    string    `json:"customerId"`
	CustomerName  string    `json:"customerName"`
	CustomerEmail string    `json:"customerEmail"`
	CustomerPhone string    `json:"customerPhone"`
	OrderNumber   string    `json:"orderNumber" gorm:"uniqueIndex;not null"`
	Status        string    `json:"status" gorm:"default:'pending'"` // pending, confirmed, preparing, ready, delivered, cancelled
	Type          string    `json:"type" gorm:"default:'dine_in'"` // dine_in, takeaway, delivery
	
	// Pricing
	Subtotal      float64 `json:"subtotal" gorm:"not null"`
	Tax           float64 `json:"tax" gorm:"default:0"`
	ServiceCharge float64 `json:"serviceCharge" gorm:"default:0"`
	Discount      float64 `json:"discount" gorm:"default:0"`
	Total         float64 `json:"total" gorm:"not null"`
	
	// Payment
	PaymentStatus string    `json:"paymentStatus" gorm:"default:'pending'"` // pending, paid, failed, refunded
	PaymentMethod string    `json:"paymentMethod"`
	PaidAt        time.Time `json:"paidAt"`
	
	// Timing
	EstimatedTime int       `json:"estimatedTime"` // in minutes
	PreparedAt    time.Time `json:"preparedAt"`
	DeliveredAt   time.Time `json:"deliveredAt"`
	
	// Special instructions
	Notes         string `json:"notes"`
	
	// Relationships
	Merchant   Merchant    `json:"merchant,omitempty"`
	Branch     Branch      `json:"branch,omitempty"`
	Table      Table       `json:"table,omitempty"`
	Customer   User        `json:"customer,omitempty"`
	OrderItems []OrderItem `json:"orderItems,omitempty"`
}

// OrderItem represents an item in an order
type OrderItem struct {
	BaseModel
	OrderID    string  `json:"orderId" gorm:"not null"`
	MenuItemID string  `json:"menuItemId" gorm:"not null"`
	Quantity   int     `json:"quantity" gorm:"not null"`
	UnitPrice  float64 `json:"unitPrice" gorm:"not null"`
	TotalPrice float64 `json:"totalPrice" gorm:"not null"`
	Notes      string  `json:"notes"`
	
	// Customizations
	Customizations string `json:"customizations" gorm:"type:jsonb"`
	
	// Status
	Status string `json:"status" gorm:"default:'pending'"` // pending, preparing, ready, served
	
	// Relationships
	Order    Order    `json:"order,omitempty"`
	MenuItem MenuItem `json:"menuItem,omitempty"`
}

// UserMerchant represents the many-to-many relationship between users and merchants
type UserMerchant struct {
	UserID     string    `json:"userId" gorm:"primaryKey"`
	MerchantID string    `json:"merchantId" gorm:"primaryKey"`
	Role       string    `json:"role" gorm:"not null"` // owner, manager, staff
	CreatedAt  time.Time `json:"createdAt" gorm:"autoCreateTime"`
	
	// Relationships
	User     User     `json:"user,omitempty"`
	Merchant Merchant `json:"merchant,omitempty"`
}

// AutoMigrate runs database migrations for all models
func AutoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&User{},
		&Merchant{},
		&Branch{},
		&MenuItem{},
		&Table{},
		&Reservation{},
		&Order{},
		&OrderItem{},
		&UserMerchant{},
	)
}
