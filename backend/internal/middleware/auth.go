package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"restaurant-backend/internal/models"
	"restaurant-backend/internal/services"
)

type AuthMiddleware struct {
	authService services.AuthService
}

func NewAuthMiddleware(authService services.AuthService) *AuthMiddleware {
	return &AuthMiddleware{
		authService: authService,
	}
}

// AuthRequired middleware validates JWT token
func (m *AuthMiddleware) AuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := extractToken(c)
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authorization token required",
			})
			c.Abort()
			return
		}

		user, err := m.authService.ValidateToken(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid or expired token",
			})
			c.Abort()
			return
		}

		// Set user in context
		c.Set("user", user)
		c.Set("userId", user.ID)
		c.Next()
	}
}

// RequireRole middleware checks if user has required role
func (m *AuthMiddleware) RequireRole(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User not authenticated",
			})
			c.Abort()
			return
		}

		userRole := user.(*models.User).Role
		hasRole := false
		for _, role := range roles {
			if userRole == role {
				hasRole = true
				break
			}
		}

		if !hasRole {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Insufficient permissions",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireMerchantAccess middleware checks if user has access to merchant
func (m *AuthMiddleware) RequireMerchantAccess() gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User not authenticated",
			})
			c.Abort()
			return
		}

		merchantID := c.Param("merchantId")
		if merchantID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Merchant ID required",
			})
			c.Abort()
			return
		}

		userObj := user.(*models.User)

		// Admin can access all merchants
		if userObj.Role == "admin" {
			c.Next()
			return
		}

		// Check if user has access to this merchant
		hasAccess := false
		for _, merchant := range userObj.Merchants {
			if merchant.ID == merchantID {
				hasAccess = true
				break
			}
		}

		if !hasAccess {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Access denied to this merchant",
			})
			c.Abort()
			return
		}

		c.Set("merchantId", merchantID)
		c.Next()
	}
}

// OptionalAuth middleware validates token if present but doesn't require it
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := extractToken(c)
		if token != "" {
			user, err := m.authService.ValidateToken(token)
			if err == nil {
				c.Set("user", user)
				c.Set("userId", user.ID)
			}
		}
		c.Next()
	}
}

// extractToken extracts JWT token from Authorization header
func extractToken(c *gin.Context) string {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		return ""
	}

	// Check for Bearer token
	if strings.HasPrefix(authHeader, "Bearer ") {
		return strings.TrimPrefix(authHeader, "Bearer ")
	}

	return ""
}
