package services

import (
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
)

type AuthService interface {
	Login(email, password string) (*AuthResponse, error)
	Register(userData *RegisterRequest) (*models.User, error)
	RefreshToken(refreshToken string) (*AuthResponse, error)
	ValidateToken(tokenString string) (*models.User, error)
	GenerateTokens(user *models.User) (*AuthResponse, error)
}

type AuthResponse struct {
	User         *models.User `json:"user"`
	AccessToken  string       `json:"accessToken"`
	RefreshToken string       `json:"refreshToken"`
	ExpiresAt    time.Time    `json:"expiresAt"`
}

type RegisterRequest struct {
	Name     string `json:"name" validate:"required,min=2,max=100"`
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=8"`
	Phone    string `json:"phone"`
	Role     string `json:"role"`
}

type LoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

type Claims struct {
	UserID string `json:"userId"`
	Email  string `json:"email"`
	Role   string `json:"role"`
	jwt.RegisteredClaims
}

type authService struct {
	userRepo  repositories.UserRepository
	jwtSecret string
}

func NewAuthService(userRepo repositories.UserRepository, jwtSecret string) AuthService {
	return &authService{
		userRepo:  userRepo,
		jwtSecret: jwtSecret,
	}
}

func (s *authService) Login(email, password string) (*AuthResponse, error) {
	// Get user by email
	user, err := s.userRepo.GetByEmail(email)
	if err != nil {
		return nil, errors.New("invalid email or password")
	}

	// Verify password
	if !s.userRepo.VerifyPassword(user, password) {
		return nil, errors.New("invalid email or password")
	}

	// Update last login
	if err := s.userRepo.UpdateLastLogin(user.ID); err != nil {
		// Log error but don't fail login
	}

	// Generate tokens
	authResponse, err := s.GenerateTokens(user)
	if err != nil {
		return nil, errors.New("failed to generate tokens")
	}

	return authResponse, nil
}

func (s *authService) Register(userData *RegisterRequest) (*models.User, error) {
	// Check if user already exists
	existingUser, _ := s.userRepo.GetByEmail(userData.Email)
	if existingUser != nil {
		return nil, errors.New("user with this email already exists")
	}

	// Set default role if not provided
	if userData.Role == "" {
		userData.Role = "user"
	}

	// Create user
	user := &models.User{
		Name:         userData.Name,
		Email:        userData.Email,
		PasswordHash: userData.Password, // Will be hashed in repository
		Phone:        userData.Phone,
		Role:         userData.Role,
	}

	err := s.userRepo.Create(user)
	if err != nil {
		return nil, errors.New("failed to create user")
	}

	// Clear password hash from response
	user.PasswordHash = ""

	return user, nil
}

func (s *authService) RefreshToken(refreshToken string) (*AuthResponse, error) {
	// Parse and validate refresh token
	token, err := jwt.ParseWithClaims(refreshToken, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(s.jwtSecret), nil
	})

	if err != nil || !token.Valid {
		return nil, errors.New("invalid refresh token")
	}

	claims, ok := token.Claims.(*Claims)
	if !ok {
		return nil, errors.New("invalid token claims")
	}

	// Get user
	user, err := s.userRepo.GetByID(claims.UserID)
	if err != nil {
		return nil, errors.New("user not found")
	}

	// Generate new tokens
	authResponse, err := s.GenerateTokens(user)
	if err != nil {
		return nil, errors.New("failed to generate tokens")
	}

	return authResponse, nil
}

func (s *authService) ValidateToken(tokenString string) (*models.User, error) {
	// Parse token
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(s.jwtSecret), nil
	})

	if err != nil || !token.Valid {
		return nil, errors.New("invalid token")
	}

	claims, ok := token.Claims.(*Claims)
	if !ok {
		return nil, errors.New("invalid token claims")
	}

	// Get user
	user, err := s.userRepo.GetByID(claims.UserID)
	if err != nil {
		return nil, errors.New("user not found")
	}

	return user, nil
}

func (s *authService) GenerateTokens(user *models.User) (*AuthResponse, error) {
	// Access token (short-lived)
	accessTokenExpiry := time.Now().Add(24 * time.Hour)
	accessClaims := &Claims{
		UserID: user.ID,
		Email:  user.Email,
		Role:   user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(accessTokenExpiry),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Subject:   user.ID,
		},
	}

	accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims)
	accessTokenString, err := accessToken.SignedString([]byte(s.jwtSecret))
	if err != nil {
		return nil, err
	}

	// Refresh token (long-lived)
	refreshTokenExpiry := time.Now().Add(7 * 24 * time.Hour)
	refreshClaims := &Claims{
		UserID: user.ID,
		Email:  user.Email,
		Role:   user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(refreshTokenExpiry),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Subject:   user.ID,
		},
	}

	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
	refreshTokenString, err := refreshToken.SignedString([]byte(s.jwtSecret))
	if err != nil {
		return nil, err
	}

	// Clear password hash from user
	userCopy := *user
	userCopy.PasswordHash = ""

	return &AuthResponse{
		User:         &userCopy,
		AccessToken:  accessTokenString,
		RefreshToken: refreshTokenString,
		ExpiresAt:    accessTokenExpiry,
	}, nil
}
