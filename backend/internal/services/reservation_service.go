package services

import (
	"errors"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
)

type ReservationService interface {
	CreateReservation(req *CreateReservationRequest) (*models.Reservation, error)
	UpdateReservation(id string, req *UpdateReservationRequest) (*models.Reservation, error)
	GetReservation(id string) (*models.Reservation, error)
	GetReservations(merchantID string, filters repositories.ReservationFilters) ([]models.Reservation, *repositories.PaginationResult, error)
	CancelReservation(id string) error
	ConfirmReservation(id string) error
	CompleteReservation(id string) error
	CheckAvailability(req *AvailabilityRequest) (*repositories.AvailabilityResult, error)
	GetReservationStats(merchantID string, period string) (*repositories.ReservationStats, error)
}

type CreateReservationRequest struct {
	MerchantID       string `json:"merchantId" validate:"required"`
	BranchID         string `json:"branchId"`
	TableID          string `json:"tableId"`
	CustomerID       string `json:"customerId"`
	CustomerName     string `json:"customerName" validate:"required"`
	CustomerEmail    string `json:"customerEmail" validate:"email"`
	CustomerPhone    string `json:"customerPhone"`
	Date             string `json:"date" validate:"required"`
	Time             string `json:"time" validate:"required"`
	PartySize        int    `json:"partySize" validate:"required,min=1,max=20"`
	SpecialRequests  string `json:"specialRequests"`
	Notes            string `json:"notes"`
}

type UpdateReservationRequest struct {
	CustomerName     string `json:"customerName"`
	CustomerEmail    string `json:"customerEmail" validate:"omitempty,email"`
	CustomerPhone    string `json:"customerPhone"`
	Date             string `json:"date"`
	Time             string `json:"time"`
	PartySize        int    `json:"partySize" validate:"omitempty,min=1,max=20"`
	Status           string `json:"status"`
	TableID          string `json:"tableId"`
	SpecialRequests  string `json:"specialRequests"`
	Notes            string `json:"notes"`
}

type AvailabilityRequest struct {
	MerchantID            string `json:"merchantId" validate:"required"`
	Date                  string `json:"date" validate:"required"`
	Time                  string `json:"time" validate:"required"`
	PartySize             int    `json:"partySize" validate:"required,min=1,max=20"`
	TableID               string `json:"tableId"`
	ExcludeReservationID  string `json:"excludeReservationId"`
}

type reservationService struct {
	reservationRepo repositories.ReservationRepository
}

func NewReservationService(reservationRepo repositories.ReservationRepository) ReservationService {
	return &reservationService{
		reservationRepo: reservationRepo,
	}
}

func (s *reservationService) CreateReservation(req *CreateReservationRequest) (*models.Reservation, error) {
	// Parse date
	date, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		return nil, errors.New("invalid date format")
	}

	// Validate date is not in the past
	if date.Before(time.Now().Truncate(24 * time.Hour)) {
		return nil, errors.New("reservation date cannot be in the past")
	}

	// Validate time format
	if !isValidTimeFormat(req.Time) {
		return nil, errors.New("invalid time format (use HH:MM)")
	}

	// Check availability if table is specified
	if req.TableID != "" {
		availability, err := s.reservationRepo.CheckAvailability(
			req.MerchantID,
			date,
			req.Time,
			req.PartySize,
			"",
		)
		if err != nil {
			return nil, errors.New("failed to check availability")
		}

		if !availability.Available {
			return nil, errors.New("requested time slot is not available")
		}
	}

	// Create reservation
	reservation := &models.Reservation{
		MerchantID:      req.MerchantID,
		BranchID:        req.BranchID,
		TableID:         req.TableID,
		CustomerID:      req.CustomerID,
		CustomerName:    req.CustomerName,
		CustomerEmail:   req.CustomerEmail,
		CustomerPhone:   req.CustomerPhone,
		Date:            date,
		Time:            req.Time,
		PartySize:       req.PartySize,
		Status:          "pending",
		SpecialRequests: req.SpecialRequests,
		Notes:           req.Notes,
	}

	err = s.reservationRepo.Create(reservation)
	if err != nil {
		return nil, errors.New("failed to create reservation")
	}

	return reservation, nil
}

func (s *reservationService) UpdateReservation(id string, req *UpdateReservationRequest) (*models.Reservation, error) {
	// Get existing reservation
	reservation, err := s.reservationRepo.GetByID(id)
	if err != nil {
		return nil, errors.New("reservation not found")
	}

	// Update fields if provided
	if req.CustomerName != "" {
		reservation.CustomerName = req.CustomerName
	}
	if req.CustomerEmail != "" {
		reservation.CustomerEmail = req.CustomerEmail
	}
	if req.CustomerPhone != "" {
		reservation.CustomerPhone = req.CustomerPhone
	}
	if req.Date != "" {
		date, err := time.Parse("2006-01-02", req.Date)
		if err != nil {
			return nil, errors.New("invalid date format")
		}
		reservation.Date = date
	}
	if req.Time != "" {
		if !isValidTimeFormat(req.Time) {
			return nil, errors.New("invalid time format (use HH:MM)")
		}
		reservation.Time = req.Time
	}
	if req.PartySize > 0 {
		reservation.PartySize = req.PartySize
	}
	if req.Status != "" {
		if !isValidStatus(req.Status) {
			return nil, errors.New("invalid status")
		}
		reservation.Status = req.Status
	}
	if req.TableID != "" {
		reservation.TableID = req.TableID
	}
	if req.SpecialRequests != "" {
		reservation.SpecialRequests = req.SpecialRequests
	}
	if req.Notes != "" {
		reservation.Notes = req.Notes
	}

	// Check availability if date, time, or table changed
	if req.Date != "" || req.Time != "" || req.TableID != "" {
		availability, err := s.reservationRepo.CheckAvailability(
			reservation.MerchantID,
			reservation.Date,
			reservation.Time,
			reservation.PartySize,
			id,
		)
		if err != nil {
			return nil, errors.New("failed to check availability")
		}

		if !availability.Available {
			return nil, errors.New("requested time slot is not available")
		}
	}

	err = s.reservationRepo.Update(reservation)
	if err != nil {
		return nil, errors.New("failed to update reservation")
	}

	return reservation, nil
}

func (s *reservationService) GetReservation(id string) (*models.Reservation, error) {
	return s.reservationRepo.GetByID(id)
}

func (s *reservationService) GetReservations(merchantID string, filters repositories.ReservationFilters) ([]models.Reservation, *repositories.PaginationResult, error) {
	return s.reservationRepo.GetByMerchantID(merchantID, filters)
}

func (s *reservationService) CancelReservation(id string) error {
	reservation, err := s.reservationRepo.GetByID(id)
	if err != nil {
		return errors.New("reservation not found")
	}

	if reservation.Status == "cancelled" {
		return errors.New("reservation is already cancelled")
	}

	reservation.Status = "cancelled"
	return s.reservationRepo.Update(reservation)
}

func (s *reservationService) ConfirmReservation(id string) error {
	reservation, err := s.reservationRepo.GetByID(id)
	if err != nil {
		return errors.New("reservation not found")
	}

	if reservation.Status != "pending" {
		return errors.New("only pending reservations can be confirmed")
	}

	reservation.Status = "confirmed"
	reservation.ConfirmedAt = time.Now()
	return s.reservationRepo.Update(reservation)
}

func (s *reservationService) CompleteReservation(id string) error {
	reservation, err := s.reservationRepo.GetByID(id)
	if err != nil {
		return errors.New("reservation not found")
	}

	if reservation.Status != "confirmed" {
		return errors.New("only confirmed reservations can be completed")
	}

	reservation.Status = "completed"
	return s.reservationRepo.Update(reservation)
}

func (s *reservationService) CheckAvailability(req *AvailabilityRequest) (*repositories.AvailabilityResult, error) {
	date, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		return nil, errors.New("invalid date format")
	}

	if !isValidTimeFormat(req.Time) {
		return nil, errors.New("invalid time format (use HH:MM)")
	}

	return s.reservationRepo.CheckAvailability(
		req.MerchantID,
		date,
		req.Time,
		req.PartySize,
		req.ExcludeReservationID,
	)
}

func (s *reservationService) GetReservationStats(merchantID string, period string) (*repositories.ReservationStats, error) {
	return s.reservationRepo.GetStats(merchantID, period)
}

// Helper functions
func isValidTimeFormat(timeStr string) bool {
	_, err := time.Parse("15:04", timeStr)
	return err == nil
}

func isValidStatus(status string) bool {
	validStatuses := []string{"pending", "confirmed", "completed", "cancelled", "no_show"}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}
