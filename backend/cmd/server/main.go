package main

import (
	"log"
	"os"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	"restaurant-backend/internal/config"
	"restaurant-backend/internal/handlers"
	"restaurant-backend/internal/middleware"
	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/services"
)

// @title Restaurant Management API
// @version 1.0
// @description API for restaurant management system
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// Initialize configuration
	cfg := config.Load()

	// Initialize database
	db, err := config.InitDB(cfg)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Auto-migrate database schemas
	if err := models.AutoMigrate(db); err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	// Initialize repositories
	userRepo := repositories.NewUserRepository(db)
	merchantRepo := repositories.NewMerchantRepository(db)
	menuItemRepo := repositories.NewMenuItemRepository(db)
	reservationRepo := repositories.NewReservationRepository(db)
	orderRepo := repositories.NewOrderRepository(db)

	// Initialize services
	authService := services.NewAuthService(userRepo, cfg.JWTSecret)
	merchantService := services.NewMerchantService(merchantRepo)
	menuItemService := services.NewMenuItemService(menuItemRepo)
	reservationService := services.NewReservationService(reservationRepo)
	orderService := services.NewOrderService(orderRepo)

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authService)
	merchantHandler := handlers.NewMerchantHandler(merchantService)
	menuItemHandler := handlers.NewMenuItemHandler(menuItemService)
	reservationHandler := handlers.NewReservationHandler(reservationService)
	orderHandler := handlers.NewOrderHandler(orderService)

	// Initialize Gin router
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.Default()

	// CORS middleware
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowOrigins = cfg.CORSAllowedOrigins
	corsConfig.AllowMethods = cfg.CORSAllowedMethods
	corsConfig.AllowHeaders = cfg.CORSAllowedHeaders
	corsConfig.AllowCredentials = true
	router.Use(cors.New(corsConfig))

	// Global middleware
	router.Use(middleware.Logger())
	router.Use(middleware.ErrorHandler())

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "Restaurant API is running",
		})
	})

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Auth routes
		auth := v1.Group("/auth")
		{
			auth.POST("/login", authHandler.Login)
			auth.POST("/register", authHandler.Register)
			auth.POST("/refresh", authHandler.RefreshToken)
			auth.POST("/logout", middleware.AuthRequired(), authHandler.Logout)
		}

		// Merchant routes
		merchants := v1.Group("/merchants")
		merchants.Use(middleware.AuthRequired())
		{
			merchants.GET("", merchantHandler.GetMerchants)
			merchants.POST("", merchantHandler.CreateMerchant)
			merchants.GET("/:id", merchantHandler.GetMerchant)
			merchants.PUT("/:id", merchantHandler.UpdateMerchant)
			merchants.DELETE("/:id", merchantHandler.DeleteMerchant)

			// Menu items routes
			merchants.GET("/:merchantId/menu-items", menuItemHandler.GetMenuItems)
			merchants.POST("/:merchantId/menu-items", menuItemHandler.CreateMenuItem)
			merchants.GET("/:merchantId/menu-items/:id", menuItemHandler.GetMenuItem)
			merchants.PUT("/:merchantId/menu-items/:id", menuItemHandler.UpdateMenuItem)
			merchants.DELETE("/:merchantId/menu-items/:id", menuItemHandler.DeleteMenuItem)

			// Reservations routes
			merchants.GET("/:merchantId/reservations", reservationHandler.GetReservations)
			merchants.POST("/:merchantId/reservations", reservationHandler.CreateReservation)
			merchants.GET("/:merchantId/reservations/:id", reservationHandler.GetReservation)
			merchants.PUT("/:merchantId/reservations/:id", reservationHandler.UpdateReservation)
			merchants.DELETE("/:merchantId/reservations/:id", reservationHandler.CancelReservation)

			// Orders routes
			merchants.GET("/:merchantId/orders", orderHandler.GetOrders)
			merchants.POST("/:merchantId/orders", orderHandler.CreateOrder)
			merchants.GET("/:merchantId/orders/:id", orderHandler.GetOrder)
			merchants.PUT("/:merchantId/orders/:id", orderHandler.UpdateOrderStatus)
		}
	}

	// Swagger documentation
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("Server starting on port %s", port)
	if err := router.Run(":" + port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
