version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: restaurant_postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: restaurant_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - restaurant_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: restaurant_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - restaurant_network

  # Go Backend API
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: restaurant_api
    environment:
      - PORT=8080
      - GIN_MODE=debug
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=password
      - DB_NAME=restaurant_db
      - DB_SSL_MODE=disable
      - JWT_SECRET=your-super-secret-jwt-key
      - JWT_EXPIRY=24h
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:4000
      - CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
      - CORS_ALLOWED_HEADERS=Content-Type,Authorization
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_DB=0
    ports:
      - "8080:8080"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./uploads:/app/uploads
    networks:
      - restaurant_network
    restart: unless-stopped

  # pgAdmin (Optional - for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: restaurant_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - restaurant_network
    profiles:
      - tools

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  restaurant_network:
    driver: bridge
